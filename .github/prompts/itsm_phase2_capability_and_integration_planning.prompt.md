---
mode: "agent"
description: "Phase 2: Analyze ITSM API capabilities and draft integration plan."
---

# Phase 2: ITSM Capability & Integration Planning

## OBJECTIVE

Enumerate supported ITSM capabilities and draft a plan for integrating with ${input:variableName:product_name} APIs, including authentication, endpoint usage, and error handling.

## SUCCESS CRITERIA

- All supported ITSM capabilities are listed and described
- For each capability, relevant API endpoints and required parameters are specified
- A high-level integration plan is drafted, including authentication, endpoint usage, and error handling
- Any gaps or missing information are clearly marked as "REQUIRES VALIDATION"
- No fabricated or speculative information is included
- If critical schema or capability information is missing, a "BLOCKER" is output and the process halts
- Output is actionable for the next phase

## CRITICAL: OUTPUT FILE FOR PHASE CHAINING

After completing the analysis, create an output file for Phase 3 chaining:

```
Create file: tmp/itsm_phase2_output_${input:variableName:product_name}.md
Content: Complete Phase 2 ITSM capability and integration planning including:
- List of supported ITSM capabilities
- For each capability, relevant API endpoints and required parameters
- High-level integration plan (authentication, endpoint usage, error handling)
- Any capabilities or integration steps where information is incomplete must be marked as "REQUIRES VALIDATION"
- If no complete schema or capability information is available, output a "BLOCKER" status and do not proceed to Phase 3
- Clear validation requirements for Phase 3
- Timestamp and completion metadata
```

This file will be automatically used as input for Phase 3: ITSM Implementation Documentation.

## CRITICAL CONSTRAINTS

- **VALIDATED_DATA_ONLY:** Use only confirmed API documentation or live responses.
- **NO_ASSUMPTIONS:** Do not infer capabilities or integration steps not explicitly documented.
- **CAPABILITY_MARKERS:** Mark any capability or integration step as "REQUIRES VALIDATION" if not fully documented.
- **SCHEMA_COMPLETENESS_REQUIRED:** If endpoint schemas are incomplete, output a "BLOCKER" and halt.
- **NO_CODE_GENERATION:** Do not include code snippets, only integration specifications.
- **CODEBASE_REVIEW:** Review the codebase for any existing ITSM integrations and reference relevant patterns or lessons learned in your analysis.

## PRE-EXECUTION VERIFICATION

Before proceeding, answer:

1. How will you identify and handle gaps in capability or integration documentation?
2. What notation will you use for items requiring validation?
3. How will you ensure all listed capabilities and integration steps are supported by real API evidence?
4. How will you incorporate findings from existing ITSM integrations in the codebase into your analysis?

## CORE TASKS

- List all supported ITSM capabilities (e.g., ticket creation, status updates, user management).
- For each capability, specify the relevant API endpoints and required parameters.
- Draft a high-level integration plan, including authentication, endpoint usage, and error handling.
- Review the codebase for any existing ITSM integrations and reference relevant patterns or lessons learned.
- Mark any gaps or missing information clearly.
- Output a "BLOCKER" if critical schema or capability information is missing.

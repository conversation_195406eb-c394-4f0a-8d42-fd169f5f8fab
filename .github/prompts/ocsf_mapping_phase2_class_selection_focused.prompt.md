---
mode: "agent"
description: "Phase 2 Focused: Select optimal OCSF class using validated API response data"
---

# Phase 2 Focused: OCSF Class Selection

## OBJECTIVE

Analyze validated ${input:variableName:product_name} API response data to select the optimal OCSF 1.5 class for mapping.

## CRITICAL CONSTRAINTS

- **VALIDATED_DATA_ONLY**: Use only confirmed API response examples from Phase 1 alternative sources
- **NO_ASSUMPTIONS**: Do not guess field structures or select classes based on incomplete data
- **CODEBASE_PATTERNS**: Reference existing vendor integrations in codebase for similar security products
- **NEEDS_LIVE_VALIDATION**: Mark decisions requiring live API testing for confirmation
- **SCHEMA_COMPLETENESS_REQUIRED**: Before class selection, confirm that a complete, real API response schema is available for each endpoint. If the schema is incomplete, output a "BLOCKER" status and do not proceed to mapping.

## PRE-EXECUTION VERIFICATION

Before proceeding, verify how you'll apply these constraints by answering:

1. How will you identify and handle gaps in API response data without making assumptions?
2. What specific notation will you use to indicate when a decision requires validation?
3. How will you approach class selection when documentation is incomplete?
4. How will you ensure a complete schema is present before allowing the process to continue?

## CONSTRAINT INTERPRETATION

Please explain your understanding of:

- The difference between working with verified API examples versus documentation descriptions
- When it's appropriate to suggest a class based on limited information versus requiring validation
- How you'll indicate uncertainty in class selection recommendations
- When to output a "BLOCKER" status and halt the process

## CORE TASK

Compare actual API response structures against OCSF classes available in our codebase implementation:

- Detection Finding (findings, alerts, detections)
- Security Finding (vulnerabilities, compliance)
- Network Activity (network traffic, connections)
- File Activity (file operations, malware analysis)

## OUTPUT REQUIREMENTS

1. **Data Structure Analysis** - Field inventory from validated API responses only. All mapping and class selection decisions must reference the full schema, not partial or inferred data.
2. **OCSF Class Comparison** - How each response maps to available OCSF classes
3. **Primary Class Recommendation** - Best fit with detailed justification
4. **Profile Requirements** - SecurityControl, Host, OSINT profile needs
5. **Validation Notes** - Fields/decisions requiring live API confirmation
6. If the schema is incomplete, output a "BLOCKER" status and do not proceed to mapping.

## SUCCESS CRITERIA

- Class selection based on actual data structures, not assumptions
- Clear mapping rationale with codebase implementation references
- Identified gaps requiring live validation in Phase 3
- At least one complete, real API response schema is present, or a "BLOCKER" status if not possible

**CRITICAL**: After completing the analysis, create an output file for Phase 3 chaining:

```
Create file: tmp/ocsf_mapping_phase2_output_${product_name}.md
Content: Complete Phase 2 OCSF class selection including:
- Data structure analysis from validated API responses
- OCSF class comparison with mapping rationale
- Primary class recommendation with detailed justification
- Profile requirements (SecurityControl, Host, OSINT)
- Validation notes for fields requiring live API confirmation
- If the schema is incomplete, output a "BLOCKER" status and do not proceed to mapping.
- Timestamp and completion metadata
```

This file will be automatically used as input for Phase 3: Field Mapping.

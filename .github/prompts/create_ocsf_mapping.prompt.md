---
mode: "agent"
description: "Generates a new confluence page documenting a mapping from api responses to OCSF classes"
---

# Security Product API Research and OCSF Mapping Task Template

# DOCUMENTATION TASK ONLY - NO CODE IMPLEMENTATION

## YOUR ROLE

You are a senior security engineer and integration specialist with expertise in:

- Security product APIs and data formats
- OCSF (Open Cybersecurity Schema Framework) schema design
- Django-based data connector implementations
- Security event normalization and transformation

Your task is to conduct thorough research and create implementable documentation for integrating ${input:variableName:product_name} with our data connectors framework.

## OBJECTIVE

Research the ${input:variableName:product_name} API endpoints and alternative data collection methods for security event/alert ingestion, determine how to transform
these events into OCSF version 1.5 format, and document the mapping for implementation in our Django-based data connectors project. The result should be a comprehensive
mapping documented in a Confluence page that can be used as a reference for implementation.

## RESEARCH METHODOLOGY

### Primary Sources (in order of preference):

1. Official API documentation and developer portals
2. GitHub repositories (official SDKs, code samples)
3. Postman collections or OpenAPI specifications
4. Technical blogs and whitepapers from the vendor
5. Community forums and Stack Overflow discussions

### Research Guidelines:

- **API Versions**: Include both stable/GA and beta/preview endpoints, but clearly mark beta/preview status
- **Source Validation**: Cross-reference information across multiple sources when possible
- **Gaps Handling**: When information is unavailable or conflicting, document this explicitly rather than making assumptions.
- **Attribution**: Include proper source links and timestamps for all references

## SPECIFIC TASKS

1. **API Discovery**: Research ALL available data collection methods:

   - REST API endpoints for security events, alerts, or incidents
   - Webhook/streaming endpoints if available
   - File export capabilities (CSV, JSON, etc.)
   - Alternative data access methods (SIEM integrations, log forwarding, etc.)

2. **Data Analysis**:

   - Locate actual JSON response examples from reliable sources
   - Document API versioning and endpoint stability (GA vs beta/preview)
   - Identify data freshness, retention policies, and availability windows

3. **OCSF Schema Mapping**:

   - Identify corresponding OCSF schema components for ${input:variableName:product_name} security events
   - Reference existing implementations in `apps/connectors/integrations/vendors/` for patterns
   - Create detailed mapping between ${input:variableName:product_name} event fields and OCSF format

4. **Compliance Considerations** (if applicable):
   - Document any PII or sensitive data handling requirements
   - Note GDPR, CCPA, or other compliance considerations
   - Identify data classification and retention implications

## CONSTRAINTS

- **Data Authenticity**: Use only actual response examples or documented fields from reliable sources - DO NOT fabricate data or field structures
- **Uncertainty Handling**: When uncertain about a mapping, provide options or indicate what additional information would be needed
- **Complete Documentation**: Document ALL fields from API responses in mapping tables, even those that won't be mapped to OCSF
- **Explicit Reasoning**: Provide clear reasons for any fields that are not mapped (e.g., PII concerns, redundancy, out of scope)
- **Beta/Preview Marking**: Clearly mark any beta or preview endpoints and note stability expectations

## EXPECTED OUTPUT STRUCTURE

Create a Confluence page titled: **"${input:variableName:product_name} to OCSF Mapping Analysis"**

### Required Sections:

#### 1. **Executive Summary**

- Brief overview of ${input:variableName:product_name} and its security capabilities
- Key security events/logs provided by this vendor
- Value proposition and business justification for this integration
- High-level implementation complexity assessment

#### 2. **Data Collection Methods**

- **Primary API Endpoints**

  - REST API endpoints with full documentation links
  - API version information and stability status (GA/beta/preview)
  - Authentication mechanisms and required credentials
  - Rate limits, pagination, and quota restrictions

- **Alternative Collection Methods**
  - Webhook/streaming capabilities
  - File export options (formats, scheduling, delivery methods)
  - SIEM integrations or log forwarding options
  - Comparative analysis of collection method benefits/limitations

#### 3. **API Response Examples**

- Complete, unmodified API response examples showing ALL fields
- Multiple examples covering different event types/scenarios
- Source attribution for each example (documentation page, version, date accessed)
- Clear marking of beta/preview endpoint examples

#### 4. **OCSF Schema Analysis**

- **Target OCSF Class Selection**

  - Recommended OCSF class(es) with justification
  - Alternative OCSF classes considered and reasoning for rejection
  - Reference to similar implementations in existing codebase

- **Schema Mapping Tables**

  - Event type/activity mappings to OCSF categories
  - Status code mappings to OCSF status values
  - Detailed field mapping with columns:
    - Source Field (API field path)
    - OCSF Target Field
    - Data Type Transformation
    - Mapping Notes/Logic
    - Ignored Reason (for unmapped fields)

- **Gap Analysis**
  - OCSF fields not available in source data
  - Source fields that don't map to OCSF
  - Recommended enrichment or transformation strategies

#### 5. **Implementation Guidance**

- **Django Integration Strategy**

  - Recommended file structure under `apps/connectors/integrations/vendors/${input:variableName:product_name}/`

- **Data Handling Considerations**

  - PII identification and handling approach
  - Data validation and sanitization requirements
  - Error scenarios and data quality issues

- **Compliance Notes** (if applicable)
  - Data retention and deletion requirements
  - Regional data handling restrictions
  - Audit trail and logging considerations

#### 6. **Research Notes and Assumptions**

- Key assumptions made during mapping process
- Areas requiring clarification or additional research
- Known limitations or challenges identified

#### 7. **References and Sources**

- API documentation links with access dates
- OCSF schema references used
- Similar integration examples from codebase
- Additional resources for implementation team

## VALIDATION CRITERIA

Your research is complete when:

- [ ] All available data collection methods are identified and compared
- [ ] Complete field mapping covers 100% of API response fields
- [ ] All beta/preview endpoints are clearly marked
- [ ] All sources are properly attributed with access timestamps

## SCOPE BOUNDARIES - IMPORTANT

- Focus on security events, alerts, and incident data
- Prioritize real-time or near-real-time data collection methods
- Document administrative/configuration APIs only if they impact data collection
- If ${input:variableName:product_name} has multiple product lines, focus on the core security platform first
- DO NOT create any new code or integration
- DO NOT run scripts to implement the integration
- DO NOT modify any existing code files
- This is ONLY a documentation and research task

## ESCALATION TRIGGERS

Reach out for guidance when:

- Official API documentation is unavailable or severely limited
- Multiple conflicting data formats are found across sources
- Technical access restrictions prevent API exploration
- OCSF mapping presents fundamental compatibility issues

---
mode: "agent"
description: "Phase 1 Focused: Identify API capabilities without JavaScript rendering limitations"
---

# Phase 1 Focused: API Discovery

## OBJECTIVE

Discover ${input:variableName:product_name} API capabilities and identify data sources for OCSF mapping without fabricating examples.

## CRITICAL CONSTRAINTS

- **JAVASCRIPT_AWARE**: Identify if official documentation uses JavaScript rendering that prevents API response viewing
- **NO_FABRICATION**: Do not create fake API response examples or guess data structures
- **REAL_SOURCES_ONLY**: Find actual accessible API documentation or alternative data sources
- **NEEDS_LIVE_VALIDATION**: Mark when live API testing is required due to documentation limitations
- **SCHEMA_REQUIRED**: You MUST include at least one complete, real API response schema or example (table or JSON) for each relevant endpoint before proceeding. If a full schema cannot be obtained, output a "BLOCKER" status and do not proceed to Phase 2.
- **FIELD_MARKERS**: For each endpoint, mark any missing fields as "MISSING: requires live validation".

## PRE-EXECUTION VERIFICATION

Before proceeding, verify how you'll apply these constraints by answering:

1. How will you handle situations where API response examples aren't readily available?
2. What specific notation will you use to indicate validation requirements?
3. How will you approach documentation gaps without fabricating information?
4. How will you ensure a complete schema is present before allowing the process to continue?

## CONSTRAINT INTERPRETATION

Please explain your understanding of:

- What constitutes "fabrication" versus reasonable interpretation of documentation
- How to clearly distinguish between verified information and assumptions
- What specific markers you'll use to indicate when live API validation is required
- When to output a "BLOCKER" status and halt the process

## CORE TASKS

1. **Product Analysis** - Security product type, primary use cases, data types
2. **API Documentation Review** - Authentication methods, endpoint inventory, response accessibility
3. **JavaScript Detection** - Check if docs use dynamic rendering that hides response examples
4. **Alternative Sources** - Find SDKs, GitHub examples, or community documentation with real responses
5. **Schema Extraction** - For each endpoint, provide a table or JSON schema summarizing all fields, types, and example values. Mark any missing fields as "MISSING: requires live validation".

## OUTPUT REQUIREMENTS

- Product overview with security focus areas
- API endpoint inventory with authentication requirements
- **JavaScript limitations identified** - Mark endpoints where response examples are not accessible
- Alternative data sources for obtaining real API response structures
- For each endpoint, a complete, real API response schema or example (table or JSON). Any fields or endpoints where the schema is incomplete must be marked as "MISSING: requires live validation"
- If no complete schema is available, output a "BLOCKER" status and do not proceed to Phase 2
- Clear marking of what requires live validation vs documentation-based analysis

## SUCCESS CRITERIA

- Complete API capability assessment without assumptions
- Clear identification of documentation limitations
- At least one complete, real API response schema or example per endpoint, or a "BLOCKER" status if not possible
- Actionable plan for obtaining real API response data in Phase 2

**CRITICAL**: After completing the analysis, create an output file for Phase 2 chaining:

```
Create file: tmp/ocsf_mapping_phase1_output_${product_name}.md
Content: Complete Phase 1 focused API discovery including:
- Product overview with security focus areas
- API endpoint inventory with authentication requirements
- For each endpoint, a complete, real API response schema or example (table or JSON)
- Any fields or endpoints where the schema is incomplete must be marked as "MISSING: requires live validation"
- If no complete schema is available, output a "BLOCKER" status and do not proceed to Phase 2
- JavaScript/documentation limitations identified
- Alternative data sources for real API responses
- Clear validation requirements for Phase 2
- Timestamp and completion metadata
```

This file will be automatically used as input for Phase 2: OCSF Class Selection.

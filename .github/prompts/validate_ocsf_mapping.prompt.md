---
mode: "agent"
tools: ["codebase"]
description: "Validate the OCSF mapping table"
---

## OCSF Mapping Validation Prompt Template

**Context:** I need you to thoroughly validate the OCSF (Open Cybersecurity Schema Framework) field mappings for ${input:variableName:product_name} to ensure they are accurate, complete, and follow best practices.

**Instructions:**

### 1. Schema Verification

- Examine the actual OCSF schema files in ocsf
- Verify that every target OCSF field referenced in the mapping tables actually exists in the schema
- Check field data types, constraints, and descriptions match the proposed mappings
- Identify any referenced fields that don't exist (like `attributes` properties)

### 2. Mapping Logic Validation

- Review each source field → OCSF field mapping for semantic correctness
- Verify that data types are compatible (string→string, boolean→boolean, etc.)
- Check that enum values align (if mapping to enum fields)
- Validate that nested object mappings follow OCSF structure patterns

### 3. OCSF Class Selection Review

- Confirm the chosen OCSF event class is the most appropriate
- Compare against alternative classes that might be better fits
- Verify the class supports all required fields for the use case
- Check if any profiles should be applied

### 4. Best Practices Compliance

- Verify mappings follow OCSF naming conventions and patterns
- Check that mappings use proper OCSF structure (`actor.user` vs direct user fields)
- Ensure timestamp fields use proper OCSF datetime patterns

### 5. Data Preservation Analysis

- Identify source fields that have no suitable OCSF mapping
- Assess criticality of unmapped fields for security analysis
- Propose alternative mapping strategies for important unmapped data
- Document any data loss implications

### 6. Implementation Feasibility

- Check that complex objects (arrays, nested structures) can be properly transformed
- Verify any required data transformations are clearly documented
- Identify potential parsing challenges or edge cases

### 7. Security Context Validation

- Ensure security-relevant fields are properly preserved
- Verify risk levels, trust indicators, and threat data are correctly mapped
- Check that authentication context is captured appropriately

### 8. Cross-Reference with Similar Integrations

- Compare mappings with existing similar integrations in the codebase
- Identify inconsistencies with established patterns
- Recommend alignment with existing successful implementations

**Deliverable:** Provide a detailed validation report including:

- ✅ Confirmed correct mappings
- ❌ Incorrect mappings with explanations and corrections
- ⚠️ Questionable mappings that need clarification
- 📝 Recommendations for improvement
- 🔍 Missing mappings that should be added

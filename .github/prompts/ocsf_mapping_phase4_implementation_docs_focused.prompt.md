---
mode: "agent"
description: "Phase 4 Focused: Compile final implementation documentation using validated mappings"
---

# Phase 4 Focused: Implementation Documentation

## OBJECTIVE

Compile all validated research from Phases 1-3 into comprehensive implementation documentation for ${input:variableName:product_name} OCSF integration.

## CRITICAL CONSTRAINTS

- **VERIFIED_DATA_ONLY**: Include only validated API responses and confirmed mappings
- **NO_SPECULATION**: Do not add unverified implementation details or assumptions
- **IMPLEMENTATION_READY**: Documentation must enable immediate development
- **FOLLOW_PATTERNS**: Use existing codebase vendor integration structure
- **SCHEMA_RECAP_REQUIRED**: Documentation must begin with a recap of the validated API response schema(s). All mappings and implementation details must reference the schema section. If the schema is incomplete, output a "BLOCKER" status and do not proceed.
- **NO_CODE_GENERATION**: Do not generate code snippets; focus on documentation and architecture

## PRE-EXECUTION VERIFICATION

Before proceeding, verify how you'll apply these constraints by answering:

1. How will you differentiate between verified API data and documentation-based information?
2. What specific notation will you use to indicate implementation details requiring validation?
3. How will you approach implementation guidance without speculating beyond available data?
4. How will you ensure a complete schema is present before allowing the process to continue?

## CONSTRAINT INTERPRETATION

Please explain your understanding of:

- What constitutes "speculation" versus reasonable implementation guidance
- How you'll clearly mark the source and confidence level of each implementation detail
- When to indicate "requires validation" versus providing implementation recommendations
- How you'll ensure documentation follows existing codebase patterns without fabrication
- When to output a "BLOCKER" status and halt the process

## INPUT REQUIREMENTS

- Phase 1: Product discovery and API inventory
- Phase 2: Validated data catalog with real API examples
- Phase 3: Detailed field mappings with OCSF implementation
- Existing vendor patterns from codebase analysis

## CORE TASK

Create complete integration documentation including:

1. **Schema Recap** - Recap of validated API response schema(s) (table or JSON)
2. **Product Overview** - API capabilities and authentication methods
3. **Validated Data Catalog** - Real API response examples with source attribution
4. **OCSF Mapping Specification** - Complete field mappings and transformations (must reference schema section)
5. **Django Implementation Plan** - File structure and integration patterns
6. **Compliance Considerations** - Data handling and privacy requirements

## OUTPUT REQUIREMENTS

- Single comprehensive Markdown document
- All API examples clearly marked as validated vs documentation-sourced
- Implementation file structure following existing vendor patterns
- Quality validation indicators and completion metadata
- If the schema is incomplete, output a "BLOCKER" status and do not proceed.

## SUCCESS CRITERIA

- Documentation enables immediate development start
- All technical details are implementation-ready
- No unverified assumptions or fabricated examples
- Follows established codebase patterns and conventions
- At least one complete, real API response schema is present, or a "BLOCKER" status if not possible

**CRITICAL**: After completing the documentation, create the final output file:

```
Create file: tmp/ocsf_mapping_phase4_final_${product_name}.md
Content: Complete implementation documentation including:
- Schema recap with validated API response schema(s)
- Product overview with validated API capabilities
- Complete data catalog with verified API examples
- OCSF mapping specification with field transformations (referencing schema section)
- Django integration architecture and file structure
- Compliance and data handling strategy
- Complete metadata including LLM model, timestamps, and validation status
- If the schema is incomplete, output a "BLOCKER" status and do not proceed.
```

This file contains the final deliverable ready for transfer to Confluence or development handoff.

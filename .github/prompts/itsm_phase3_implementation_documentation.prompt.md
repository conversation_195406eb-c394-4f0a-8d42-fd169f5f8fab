---
mode: "agent"
description: "Phase 3: Compile validated ITSM research into implementation-ready documentation."
---

# Phase 3: ITSM Implementation Documentation

## OBJECTIVE

Compile all validated research into comprehensive, implementation-ready documentation for ${input:variableName:product_name} ITSM integration.

## SUCCESS CRITERIA

- All validated API schemas and endpoints are recapped
- Supported capabilities and integration plan are summarized
- Step-by-step implementation guidance is provided, referencing only validated information
- Any gaps or missing information are clearly marked as "REQUIRES VALIDATION"
- No unverified or speculative implementation details are included
- If critical schema or implementation information is missing, a "BLOCKER" is output and the process halts
- Output is actionable for immediate development

## CRITICAL: OUTPUT FILE FOR PHASE CHAINING

After completing the documentation, create an output file for implementation handoff:

```
Create file: tmp/itsm_phase3_output_${input:variableName:product_name}.md
Content: Complete Phase 3 ITSM implementation documentation including:
- Recap of validated API schemas and endpoints
- Summary of supported capabilities and integration plan
- Step-by-step implementation guidance, referencing only validated information
- Any gaps or missing information must be marked as "REQUIRES VALIDATION"
- If no complete schema or implementation information is available, output a "BLOCKER" status
- Timestamp and completion metadata
```

This file will be used for implementation handoff or further review.

## CRITICAL CONSTRAINTS

- **VERIFIED_DATA_ONLY:** Include only validated API endpoints, schemas, and integration steps.
- **NO_SPECULATION:** Do not add unverified implementation details.
- **IMPLEMENTATION_READY:** Documentation must enable immediate development.
- **SCHEMA_RECAP_REQUIRED:** Begin with a recap of validated API schemas. If incomplete, output a "BLOCKER" and halt.
- **NO_CODE_GENERATION:** Do not generate code snippets; focus on documentation and architecture.

## PRE-EXECUTION VERIFICATION

Before proceeding, answer:

1. How will you differentiate between verified and unverified information?
2. What notation will you use for implementation details requiring validation?
3. How will you ensure documentation is implementation-ready and based only on validated data?

## CORE TASKS

- Recap all validated API schemas and endpoints.
- Summarize supported capabilities and integration plan.
- Provide step-by-step implementation guidance, referencing only validated information.
- Mark any gaps or missing information clearly.
- Output a "BLOCKER" if critical schema or implementation information is missing.

from collections.abc import Iterable
from enum import StrEnum
from uuid import UUID

from criticalstart.auth.v2.models import UserSession
from fastapi import HTTPException, status


class Permission(StrEnum):
    MANAGE_INTEGRATIONS = "data_connectors:manage_integrations"


def check_permissions(session: UserSession, permission: str, org_ids: Iterable[UUID]):
    for org_id in org_ids:
        if permission not in session.get_permissions(org_id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You do not have privileges to perform this action",
            )
    return True

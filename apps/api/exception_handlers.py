from criticalstart.fastapi_utils.exceptions.handlers import (
    add_exception_handlers as add_criticalstart_exception_handlers,
)
from criticalstart.fastapi_utils.exceptions.handlers import (
    error_content_factory,
    error_response_factory,
)
from fastapi import FastAPI, Request
from fastapi import status as http_status

from apps.connectors.integrations import IntegrationError
from apps.fs.errors import FileSystemPathError


async def resource_not_found_handler(request: Request, exc: FileSystemPathError):
    content = error_content_factory(
        http_status.HTTP_404_NOT_FOUND,
        "Not Found",
    )
    return error_response_factory(content)


async def value_error_handler(request: Request, exc: ValueError):
    content = error_content_factory(
        http_status.HTTP_422_UNPROCESSABLE_ENTITY,
        str(exc),
    )
    return error_response_factory(content)


async def integration_exception_handler(request: Request, exc: IntegrationError):
    content = error_content_factory(
        http_status.HTTP_400_BAD_REQUEST,
        str(exc),
    )
    return error_response_factory(content)


def add_exception_handlers(app: FastAPI):
    handlers = [
        (FileSystemPathError, resource_not_found_handler),
        (ValueError, value_error_handler),
        (IntegrationError, integration_exception_handler),
    ]

    add_criticalstart_exception_handlers(app)

    for exception, handler in handlers:
        app.add_exception_handler(exception, handler)

from typing import Any, Generic, Type, TypeVar

from criticalstart.auth.v2.models import Organization
from django.db.models import Model, QuerySet
from pydantic import BaseModel

ModelType = TypeVar("ModelType", bound=Model)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class PublicCRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model_cls: Type[ModelType]):
        self.model_cls = model_cls

    def get(self, id: Any, org: Organization) -> ModelType:
        return self.model_cls.objects.get(id=id, organization_id=org.id)

    def list(self, org: Organization, params=None) -> QuerySet[ModelType]:
        if params is None:
            params = {}  # Make sure to initialize params before using it
        if org:
            params["organization_id"] = org.id
        else:
            params["organization_id__isnull"] = True

        return self.model_cls.objects.filter(**params)

    def create(self, obj_in: CreateSchemaType, org: Organization) -> ModelType:
        return self.model_cls.objects.create(
            **obj_in.model_dump(warnings=False), organization_id=org.id
        )

    def delete(self, id: Any, org: Organization) -> ModelType:
        raise NotImplementedError()  # pragma: no cover

    def update(self, id: Any, obj_in: UpdateSchemaType, org: Organization) -> ModelType:
        obj = self.get(id, org)
        for field in obj_in.model_fields_set:
            new_value = getattr(obj_in, field)
            if isinstance(new_value, dict):
                obj_value = getattr(obj, field)
                for k, v in new_value.items():
                    obj_value[k] = v
            else:
                setattr(obj, field, new_value)
        obj.save(update_fields=obj_in.model_fields_set | self.auto_update_fields())
        return obj

    @staticmethod
    def auto_update_fields():  # pragma: no cover
        return set()

from typing import Any

from criticalstart.auth.v2.models import Organization
from django.db.models import Q

from apps.api.v1.schemas.aad_app import AadAppCreate, AadAppUpdate
from apps.connectors.models import AadApp

from .base import PublicCRUDBase


class PublicCRUDAadApp(PublicCRUDBase[AadApp, AadAppCreate, AadAppUpdate]):
    def get(self, id: Any, org: Organization, allow_global=True) -> AadApp:
        org_filter = Q(organization_id=org.id)
        if allow_global:
            org_filter |= Q(organization_id__isnull=True)

        return self.model_cls.objects.get(Q(client_id=id) & org_filter)

    def create(self, obj_in: AadAppCreate, org: Organization) -> AadApp:
        model_dict = obj_in.model_dump()
        model_dict["client_secret"] = obj_in.client_secret.get_secret_value()
        model_dict["organization_id"] = org.id
        return self.model_cls.objects.create(**model_dict)

    def update(self, id: Any, obj_in: AadAppUpdate, org: Organization) -> AadApp:
        obj = self.get(id, org, allow_global=False)

        for field in obj_in.model_fields_set:
            if field == "client_secret":
                # If the value is the mask, this means the user wants
                # to retain the current value.
                if not obj_in.client_secret.is_mask():
                    obj.client_secret = obj_in.client_secret.get_secret_value()
            else:
                setattr(obj, field, getattr(obj_in, field))

        obj.save(update_fields=obj_in.model_fields_set)
        return obj

    def delete(self, client_id, org: Organization):
        obj = self.get(client_id, org, allow_global=False)
        obj.delete()
        return obj


aad_app = PublicCRUDAadApp(AadApp)

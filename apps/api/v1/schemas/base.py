from enum import StrEnum
from typing import Generic, Sequence, TypeVar

from pydantic import BaseModel

T = TypeVar("T", bound=BaseModel)


class ListResponse(BaseModel, Generic[T]):
    items: list[T]

    @staticmethod
    def prepare(items):
        return {"items": items}


class MetaPair(BaseModel):
    id: str
    name: str


MetaEntry = Sequence[MetaPair]


def labels_to_meta(labels: dict[StrEnum, str]) -> MetaEntry:
    entries = []
    for key, label in labels.items():
        entries.append(
            MetaPair(
                id=key,
                name=label,
            )
        )
    return entries

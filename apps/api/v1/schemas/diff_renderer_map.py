from typing import Any

from apps.api.v1.schemas.integration import IntegrationPost
from apps.connectors.integrations import Template
from apps.connectors.integrations.template import TemplateVersionId
from apps.connectors.services.diff_logger.diff_renderer import (
    <PERSON><PERSON><PERSON><PERSON>,
    Diff<PERSON><PERSON>er,
    DiffRendererMap,
    EnabledActionRenderer,
)
from apps.connectors.services.diff_logger.label_maker import (
    ConfigLabelMaker,
    EnabledActionLabelMaker,
    SettingsLabelMaker,
    TitleLabelMaker,
)
from apps.connectors.services.diff_logger.mask import (
    BoolMask,
    EncryptionMask,
    IntMask,
    Mask,
    StringMask,
)


class SchemaDiffRendererMap(DiffRendererMap):
    """
    Mapper class for associating fields with their respective renderers.
    """

    def __init__(self):
        """
        Initialize the SchemaDiffRendererMap with basic and template version renderers.
        """
        self.basic_renderers = self._create_basic_renderers()
        self.template_version_renderers = self._create_template_version_renderers()

    def get_renderer(
        self,
        path: str,
        template_version: TemplateVersionId,
    ) -> DiffRenderer:
        """
        Get the renderer for the given path and template version.

        :param path: The path to get the renderer for.
        :param template_version: The template version ID to get the renderer for.
        :return: The DiffRenderer instance.
        """
        if "." not in path and path != "enabled_actions":
            return self.basic_renderers.get(path)

        template_version_key = self._renderers_key(
            template_version.technology_id, template_version.version_id
        )
        renderers = self.template_version_renderers.get(template_version_key)
        if renderers:
            return renderers.get(path)

    @classmethod
    def _create_basic_renderers(cls) -> dict[str, DiffRenderer]:
        """
        Create basic renderers for the fields.

        :return: A dictionary mapping field names to their renderers.
        """
        renderers = {}
        properties = IntegrationPost.model_json_schema()["properties"]
        for prop_name, prop_meta in properties.items():
            # Skip fields that are based on the technology/version
            if prop_name in ["config", "settings", "enabled_actions"]:
                continue

            # Default case for fields with a title
            renderers[prop_name] = ChangeRenderer(
                TitleLabelMaker(prop_meta["title"]),
                cls._get_mask(prop_meta),
            )

        return renderers

    @classmethod
    def _create_template_version_renderers(cls) -> dict[str, dict[str, DiffRenderer]]:
        """
        Create renderers for each template version.

        :return: A dictionary mapping template version keys to their renderers.
        """
        template_version_renderers = {}
        for template in Template.get_all_templates():
            for version_id, version in template.versions.items():
                renderers = {}

                # Add renderers for config properties
                config_metadata = (
                    version.connection_model.config_model.model_json_schema()
                )
                properties = config_metadata["properties"]
                for prop_name, prop_meta in properties.items():
                    prop_path = f"config.{prop_name}"
                    renderers[prop_path] = ChangeRenderer(
                        ConfigLabelMaker(prop_meta["title"]),
                        cls._get_mask(prop_meta),
                    )

                # Add renderers for settings properties
                action_metadata = version.actions_metadata
                for action_type, action_meta in action_metadata.items():
                    properties = action_meta["settings"]["properties"]
                    for prop_name, prop_meta in properties.items():
                        prop_path = f"settings.{action_type}.{prop_name}"
                        renderers[prop_path] = ChangeRenderer(
                            SettingsLabelMaker(action_meta["name"], prop_meta["title"]),
                            cls._get_mask(prop_meta),
                        )

                # Add renderer for enabled_actions
                enabled_actions_renderer = EnabledActionRenderer(BoolMask())
                for action_type, action_meta in action_metadata.items():
                    # Each action type has a unique label
                    label_maker = EnabledActionLabelMaker(action_meta["name"])
                    enabled_actions_renderer.add_label_maker(action_type, label_maker)
                renderers["enabled_actions"] = enabled_actions_renderer

                # Add the renderers to the template version renderers
                template_version_key = cls._renderers_key(template.id, version_id)
                template_version_renderers[template_version_key] = renderers

        return template_version_renderers

    @staticmethod
    def _get_mask(prop_meta: dict[str, Any]) -> Mask:
        """
        Get the appropriate mask for the given property metadata.

        :param prop_meta: The metadata of the property.
        :return: The Mask instance.
        """
        if prop_meta.get("type") == "integer":
            return IntMask()
        elif prop_meta.get("type") == "boolean":
            return BoolMask()
        elif prop_meta.get("format") == "password":
            return EncryptionMask()
        else:
            return StringMask()

    @staticmethod
    def _renderers_key(technology_id: str, version_id: str) -> str:
        """
        Generate a key for the template version renderers dictionary.

        :param technology_id: The technology ID of the template.
        :param version_id: The version ID of the template.
        :return: The generated key as a string.
        """
        return f"{technology_id}/{version_id}"


# Instantiate the SchemaDiffRendererMap
schema_diff_renderer_mapper = SchemaDiffRendererMap()

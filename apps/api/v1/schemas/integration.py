from datetime import datetime
from enum import StrEnum
from functools import cached_property
from typing import Optional
from uuid import UUID

from criticalstart.fastapi_utils.paginate.django import BaseSchemaModel
from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
    model_validator,
)

from apps.api.v1.schemas.base import MetaEntry
from apps.connectors.health_checks.components.component import (
    ComponentType,
    RequirementStatus,
    ValidationStatus,
)
from apps.connectors.integrations.actions.action import IntegrationActionType
from apps.connectors.integrations.template import (
    Template,
    TemplateVersionConfig,
    TemplateVersionSettings,
)
from apps.connectors.services import connector_service


class LogType(StrEnum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


class CoverageMode(StrEnum):
    ENABLED = "enabled"
    IGNORE = "ignore"
    NOT_APPLICABLE = "n/a"


class IntegrationStatus(StrEnum):
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

    @classmethod
    def labels(cls):
        return {
            cls.HEALTHY: "Healthy",
            cls.UNHEALTHY: "Unhealthy",
            cls.UNKNOWN: "Unknown",
        }


class HealthCheckRequirement(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    name: str
    description: str
    value: str | None
    required: RequirementStatus
    status: ValidationStatus


class HealthCheckComponent(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    id: str
    name: str
    meta: dict
    type: ComponentType
    updated: datetime
    requirements: list[HealthCheckRequirement]
    action_type: Optional[IntegrationActionType] = Field(alias="action_type")


class IntegrationBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    technology_id: str = Field(title="Technology ID")
    version_id: str = Field(title="Version ID")
    name: str = Field(default_factory=str, max_length=255, title="Name")
    # FIXME: migration_id=connection_id
    # connection_id will be required in this API after migration
    connection_id: Optional[UUID] = Field(frozen=True, default=None)


class LogLevel(StrEnum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


class ActivityLog(BaseSchemaModel):
    model_config = ConfigDict(from_attributes=True)

    timestamp: datetime
    message: str
    log_level: LogLevel


class IntegrationResponse(IntegrationBase):
    organization_id: UUID
    account_id: UUID
    validation: list[HealthCheckComponent]
    status: IntegrationStatus

    @model_validator(mode="before")
    def compute_health_check(self):
        self.validation = connector_service.get_health_check(self) or []
        self.status = IntegrationResponse.get_integration_status(self)
        return self

    @staticmethod
    def get_integration_status(connector):
        is_healthy = connector_service.is_healthy(connector, force_refresh=False)
        if is_healthy is None:
            return IntegrationStatus.UNKNOWN
        else:
            return (
                IntegrationStatus.HEALTHY if is_healthy else IntegrationStatus.UNHEALTHY
            )


class IntegrationValidationResponse(IntegrationBase):
    organization_id: UUID
    account_id: UUID
    validation: list[HealthCheckComponent]
    status: IntegrationStatus

    @model_validator(mode="before")
    def compute_health_check(self):
        self.validation = connector_service.get_onboarding_health_check(self)
        if all(component.is_healthy() for component in self.validation):
            self.status = IntegrationStatus.HEALTHY
        else:
            self.status = IntegrationStatus.UNHEALTHY
        return self


class IntegrationSummary(IntegrationResponse):
    id: UUID
    enabled: bool
    category_id: str
    category_name: str
    technology_name: str
    enabled_actions: list = Field(default_factory=list)
    last_activity_at: Optional[datetime]
    vulnerability_coverage_mode: CoverageMode
    endpoint_coverage_mode: CoverageMode
    vendor_id: str
    vendor_name: str
    modules: list[str]


class IntegrationPost(IntegrationBase):
    settings: dict[IntegrationActionType, dict] = Field(default_factory=dict)
    enabled: bool = Field(title="Enabled")
    enabled_actions: list = Field(default_factory=list, title="Enabled Actions")
    vulnerability_coverage_mode: CoverageMode = Field(
        default=CoverageMode.NOT_APPLICABLE,
        title="Vulnerability Coverage Mode",
    )
    endpoint_coverage_mode: CoverageMode = Field(
        default=CoverageMode.NOT_APPLICABLE,
        title="Endpoint Coverage Mode",
    )
    # FIXME: migration_id=connection_config
    # config will no longer be accepted in this API after migration
    config: dict | None = None

    @cached_property
    def config_model(self) -> TemplateVersionConfig | None:
        template = Template.get_template(self.technology_id)
        version = template.versions[self.version_id]
        return version.connection_model.config_model.model_validate(self.config)

    @model_validator(mode="before")
    def set_empty_enabled_based_on_enabled_actions(cls, values):
        # only needed until we deploy the new FE
        if values.get("enabled") is None:
            values["enabled"] = bool(values.get("enabled_actions"))
        return values

    def validate_config(self):
        config_model = self.config_model
        self.config = config_model.unmasked_config
        return self

    @cached_property
    def settings_model(self) -> TemplateVersionSettings:
        template = Template.get_template(self.technology_id)
        version = template.versions[self.version_id]
        return version.settings_model.model_validate(self.settings)

    @model_validator(mode="after")
    def validate_settings(self):
        # We validate and dump again to set default settings
        self.settings = self.settings_model.model_dump(mode="json")
        return self


class IntegrationCreate(IntegrationPost):
    pass


class IntegrationUpdate(IntegrationPost):
    pass


class IntegrationVulnerabilityUpdate(BaseModel):
    vulnerability_coverage_mode: CoverageMode


class IntegrationEndpointCoverageModeUpdate(BaseModel):
    endpoint_coverage_mode: CoverageMode


class Integration(IntegrationResponse):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    technology_name: str
    category_name: str
    config: dict
    settings: dict
    enabled: bool
    enabled_actions: list = Field(default_factory=list)
    last_activity_at: Optional[datetime]
    vulnerability_coverage_mode: CoverageMode
    endpoint_coverage_mode: CoverageMode
    product_id: Optional[int]
    config_managed_by_portal: bool

    @field_validator("config", mode="before")
    def validate_config(cls, config, info):
        template = Template.get_template(info.data["technology_id"])
        version = template.versions[info.data["version_id"]]
        # load the config into the model and dump it back out to ensure secrets are masked
        return version.connection_model.config_model.model_validate(config).model_dump(
            mode="json"
        )


class IntegrationMeta(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    status: MetaEntry

from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from apps.connectors.integrations.types import EncryptedStr


class AadApp(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    client_id: UUID
    name: str
    organization_id: UUID | None


class ListAadApp(BaseModel):
    items: list[AadApp]


class AadAppCreate(BaseModel):
    client_id: UUID
    client_secret: EncryptedStr = Field(max_length=1024)
    name: str


class AadAppUpdate(BaseModel):
    client_secret: Optional[EncryptedStr] = Field(max_length=1024, default=None)
    name: Optional[str] = Field(default=None)

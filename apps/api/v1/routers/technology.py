from fastapi import APIRouter
from fastapi.exceptions import HTTPException
from fastapi.responses import FileResponse
from starlette.status import HTTP_404_NOT_FOUND

from apps.api.deps import SessionDep
from apps.api.v1.schemas.base import ListResponse
from apps.api.v1.schemas.technology import TechnologySummary, TechnologyVersion
from apps.connectors.integrations import Template

router = APIRouter(
    prefix="/technologies",
    tags=["technologies"],
)


@router.get("")
def list_technologies(session: SessionDep) -> ListResponse[TechnologySummary]:
    return ListResponse.prepare(
        list(filter(session.is_available, Template.get_all_templates()))
    )


@router.get("/import_hosts/versions/v1/import_template")
def get_import_template() -> FileResponse:
    return FileResponse(
        path="apps/static_files/import_asset_template.xlsx",
        filename="import_asset_template.xlsx",
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        status_code=200,
    )


@router.get("/{technology_id}/versions", response_model_exclude_unset=True)
def list_technology_versions(
    technology_id: str,
    session: SessionDep,
) -> ListResponse[TechnologyVersion]:
    template = Template.get_template(technology_id)
    if not session.is_available(template):
        raise HTTPException(status_code=HTTP_404_NOT_FOUND)
    return ListResponse.prepare(list(template.versions.values()))


@router.get("/{technology_id}/versions/{version_id}", response_model_exclude_unset=True)
def get_technology_version(
    technology_id: str,
    version_id: str,
    session: SessionDep,
) -> TechnologyVersion:
    if not session.is_available(Template.get_template(technology_id)):
        raise HTTPException(status_code=HTTP_404_NOT_FOUND)
    return Template.get_template(technology_id).versions[version_id]

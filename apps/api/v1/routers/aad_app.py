from uuid import UUID

from criticalstart.auth.v2.integrations.fastapi import UserSession
from fastapi import APIRouter, Depends

from apps.api.deps import session
from apps.api.permissions import Permission, check_permissions
from apps.api.v1.crud import aad_app as crud
from apps.api.v1.schemas.aad_app import AadApp, AadAppCreate, AadAppUpdate, ListAadApp

router = APIRouter(
    prefix="/aad_apps",
    tags=["aad_apps"],
)


def check_manage_aadapps(user_session):
    check_permissions(
        user_session,
        Permission.MANAGE_INTEGRATIONS,
        [user_session.organization.id],
    )


@router.get("/{aad_app_client_id}", response_model=AadApp)
def get_aad_app(
    aad_app_client_id: UUID,
    user_session: UserSession = Depends(session),
) -> AadApp:
    obj = crud.get(aad_app_client_id, user_session.organization)
    return AadApp.model_validate(obj)


@router.get("")
def list_aad_apps(user_session: UserSession = Depends(session)) -> ListAadApp:
    items = list(crud.list(user_session.organization)) + list(crud.list(None))
    return ListAadApp.model_validate({"items": items})


@router.post("")
def create_aad_app(
    aad_app: AadAppCreate,
    user_session: UserSession = Depends(session),
) -> AadApp:
    check_manage_aadapps(user_session)
    obj = crud.create(aad_app, user_session.organization)
    return AadApp.model_validate(obj)


@router.put("/{aad_app_client_id}")
def update_aad_app(
    aad_app_client_id: UUID,
    aad_app: AadAppUpdate,
    user_session: UserSession = Depends(session),
) -> AadApp:
    check_manage_aadapps(user_session)
    obj = crud.update(aad_app_client_id, aad_app, user_session.organization)
    return AadApp.model_validate(obj)


@router.delete("/{aad_app_client_id}")
def delete_aad_app(
    aad_app_client_id: UUID,
    user_session: UserSession = Depends(session),
):
    check_manage_aadapps(user_session)
    crud.delete(aad_app_client_id, user_session.organization)
    return {"detail": "Deleted"}

import logging
from typing import Annotated, Optional
from uuid import UUID

from criticalstart.fastapi_utils.paginate.django import (
    <PERSON>,
    PageParams,
    make_page_params,
    paginate_no_ordering,
)
from criticalstart.fastapi_utils.parameters.period import QueryPeriodType, parse_period
from fastapi import APIRouter, Depends, Query, Response
from fastapi import status as httpstatus
from fastapi.exceptions import HTTPException

from apps.api.deps import SessionDep
from apps.api.permissions import Permission, check_permissions
from apps.api.v1.crud import connector as crud
from apps.api.v1.schemas.base import ListResponse, labels_to_meta
from apps.api.v1.schemas.diff_renderer_map import schema_diff_renderer_mapper
from apps.api.v1.schemas.integration import (
    ActivityLog,
    CoverageMode,
    Integration,
    IntegrationCreate,
    IntegrationEndpointCoverageModeUpdate,
    IntegrationMeta,
    IntegrationStatus,
    IntegrationSummary,
    IntegrationUpdate,
    IntegrationValidationResponse,
    IntegrationVulnerabilityUpdate,
    LogType,
)
from apps.connectors import models
from apps.connectors.integrations import IntegrationAction, Template
from apps.connectors.models import Connector
from apps.connectors.models.activity_log import DatabaseLogHandler
from apps.connectors.services import DiffLogger, connector_service
from apps.connectors.tasks import invoke_integration_action

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/integrations",
    tags=["integrations"],
)


def validate_integration(integration_id: UUID, user_session: SessionDep):
    """Validate that the integration exists."""
    try:
        connector = crud.get(integration_id, user_session.organization)
        if not user_session.is_available(connector):
            raise Connector.DoesNotExist()
        return connector
    except Connector.DoesNotExist:
        raise HTTPException(
            status_code=httpstatus.HTTP_404_NOT_FOUND,
            detail="Integration not found",
        )


def check_manage_integrations(user_session: SessionDep):
    check_permissions(
        user_session, Permission.MANAGE_INTEGRATIONS, [user_session.organization.id]
    )


@router.get("/meta")
def get_integration_meta() -> IntegrationMeta:
    status = labels_to_meta(IntegrationStatus.labels())
    return IntegrationMeta(status=status)


@router.get("/{integration_id}", response_model=Integration)
def get_integration(
    integration: Connector = Depends(validate_integration),
) -> Connector:
    return integration


@router.get("", response_model=ListResponse[IntegrationSummary])
def list_integrations(
    user_session: SessionDep,
    technology_id: Annotated[list[str], Query()] = None,
    vendor_id: Annotated[list[str], Query()] = None,
    category_id: Annotated[list[str], Query()] = None,
    module: Annotated[list[str], Query()] = None,
    status: Annotated[list[str], Query()] = None,
    enabled_action: Annotated[list[str] | None, Query()] = None,
):
    # Optimize for the case where no technology filters are applied
    if not any([technology_id, vendor_id, category_id]):
        technology_ids = None
    else:
        technology_ids = {
            template.id
            for template in Template.get_templates(
                vendor_ids=vendor_id, category_ids=category_id
            )
        }
        if technology_id:
            technology_ids = technology_ids.intersection(technology_id)

    connectors = crud.list(
        user_session.organization,
        technology_ids,
        enabled_actions=enabled_action,
    )
    available_connectors = []
    for connector in connectors:
        if not user_session.is_available(connector):
            continue
        if module and not connector.template_version.has_module(module):
            continue
        if (
            status
            and IntegrationSummary.get_integration_status(connector) not in status
        ):
            continue
        available_connectors.append(connector)

    return ListResponse.prepare(list(available_connectors))


@router.post("")
def create_integration(
    integration: IntegrationCreate,
    user_session: SessionDep,
) -> Integration:
    check_manage_integrations(user_session)
    template = Template.get_template(integration.technology_id)
    if not user_session.is_available(template):
        raise HTTPException(status_code=httpstatus.HTTP_404_NOT_FOUND)
    connector = crud.create(integration, user_session.organization)
    activity_logger = connector_service.get_activity_logger(connector)
    activity_logger.info("Integration created")
    return connector


@router.delete("/{integration_id}")
def delete_integration(
    integration_id: UUID,
    user_session: SessionDep,
) -> Integration:
    check_manage_integrations(user_session)
    connector = crud.delete(integration_id, user_session.organization)
    activity_logger = connector_service.get_activity_logger(connector)
    activity_logger.info("Integration deleted")
    return connector


@router.put("/{integration_id}")
def update_integration(
    integration_id: UUID,
    integration_update: IntegrationUpdate,
    user_session: SessionDep,
    connector: Connector = Depends(validate_integration),
) -> Integration:
    check_manage_integrations(user_session)
    # FIXME: migration_id=connection_config
    # Temporarily, manually log changes to connector config. In part 3 we will add logging to
    # the Connection endpoint.
    config_before = connector.config
    update = crud.update(integration_id, integration_update, user_session.organization)
    config_after = update.config

    diff_logger = DiffLogger(
        schema_diff_renderer_mapper,
        DatabaseLogHandler(connector.id),
        user_email=user_session.user.email,
    )
    diff_logger.log_config_changes(connector, config_before, config_after)
    diff_logger.log_property_changes(before=connector, after=update)
    return update


@router.put("/{integration_id}/vulnerability_coverage_mode")
def update_vulnerability_coverage_mode(
    integration_id: UUID,
    integration_vulnerability_update: IntegrationVulnerabilityUpdate,
    user_session: SessionDep,
    connector: Connector = Depends(validate_integration),
) -> Integration:
    check_manage_integrations(user_session)

    mode = integration_vulnerability_update.vulnerability_coverage_mode
    if mode == CoverageMode.IGNORE:
        has_enabled = (
            Connector.objects.exclude(id=integration_id)
            .filter(
                organization_id=user_session.organization.id,
                vulnerability_coverage_mode=CoverageMode.ENABLED,
                enabled=True,
                is_deleted=False,
            )
            .exists()
        )
        if not has_enabled:
            raise HTTPException(
                status_code=httpstatus.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="At least one integration must have vulnerability coverage mode enabled.",
            )

    update = crud.update_vulnerability_coverage_mode(
        integration_id,
        integration_vulnerability_update.vulnerability_coverage_mode,
        user_session.organization,
    )
    diff_logger = DiffLogger(
        schema_diff_renderer_mapper,
        DatabaseLogHandler(connector.id),
        user_email=user_session.user.email,
    )
    diff_logger.log_property_changes(before=connector, after=update)
    return update


@router.put("/{integration_id}/endpoint_coverage_mode")
def update_endpoint_coverage_mode(
    integration_id: UUID,
    integration_endpoint_coverage_mode_update: IntegrationEndpointCoverageModeUpdate,
    user_session: SessionDep,
    connector: Connector = Depends(validate_integration),
) -> Integration:
    check_manage_integrations(user_session)

    mode = integration_endpoint_coverage_mode_update.endpoint_coverage_mode
    if mode == CoverageMode.IGNORE:
        has_enabled = (
            Connector.objects.exclude(id=integration_id)
            .filter(
                organization_id=user_session.organization.id,
                endpoint_coverage_mode=CoverageMode.ENABLED,
                enabled=True,
                is_deleted=False,
            )
            .exists()
        )
        if not has_enabled:
            raise HTTPException(
                status_code=httpstatus.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="At least one integration must have endpoint coverage mode enabled.",
            )

    update = crud.update_endpoint_coverage_mode(
        integration_id,
        mode,
        user_session.organization,
    )
    diff_logger = DiffLogger(
        schema_diff_renderer_mapper,
        DatabaseLogHandler(connector.id),
        user_email=user_session.user.email,
    )
    diff_logger.log_property_changes(before=connector, after=update)
    return update


@router.post("/{integration_id}/refresh_health_checks", response_model=Integration)
def refresh_health_check(
    integration: Connector = Depends(validate_integration),
) -> Connector:
    connector_service.refresh_health_check(integration)
    return integration


@router.post("/validate", response_model=IntegrationValidationResponse)
def validate_connection(
    integration: IntegrationCreate, user_session: SessionDep
) -> Connector:
    check_manage_integrations(user_session)
    connector = Connector(
        **integration.model_dump(warnings=False),
        id=None,
        organization_id=user_session.organization.id,
    )

    return connector


@router.get("/{integration_id}/activity_logs")
def get_activity_logs(
    integration: Connector = Depends(validate_integration),
    timestamp: Optional[QueryPeriodType] = Query(None),
    search: Optional[str] = None,
    log_level: Optional[list[LogType]] = Query(None),
    page_params: PageParams = Depends(
        make_page_params(
            model=models.ActivityLog,
            schema=ActivityLog,
        )
    ),
) -> Page[ActivityLog]:
    timestamp = parse_period(timestamp)
    qs = connector_service.get_activity_logs(
        integration,
        start_timestamp=timestamp.start if timestamp else None,
        end_timestamp=timestamp.end if timestamp else None,
        search=search,
        log_levels=log_level,
    )
    # The ActivityLog model has a custom ordering defined in its Meta class,
    # so we use paginate_no_ordering to respect that ordering.
    return paginate_no_ordering(qs, page_params, ActivityLog)


def create_invoke_action_route(
    action_type: str,
    args_type,
):
    """
    Create a route for publicly invoking an action on an integration.
    :param action_type: The action name to create route for
    :param args_type: The type of args for the action
    """

    def invoke_action(
        action_args: args_type,
        connector: Connector = Depends(validate_integration),
    ):
        if not connector.enabled:
            logger.warning("Connector is disabled", extra=connector.log_extra)
            raise HTTPException(
                status_code=httpstatus.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Connector is disabled",
            )
        if not connector_service.is_action_healthy(connector, action_type):
            logger.warning(
                "Connector is unhealthy for action",
                extra={**connector.log_extra, "action": action_type},
            )
            raise HTTPException(
                status_code=httpstatus.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Connector is unhealthy",
            )
        if action_type not in connector.enabled_actions:
            raise HTTPException(
                status_code=httpstatus.HTTP_405_METHOD_NOT_ALLOWED,
                detail=f"Action {action_type} is not enabled for this integration",
            )

        args_dict = action_args.model_dump(mode="json", warnings=False)
        invoke_integration_action.delay(connector.id, action_type, args_dict)

    # Override the function name and qualname so that we can trace each invoke action route
    # by action name. Python module (top-level) functions have the same __name__ and __qualname__.
    # We must override these attributes before adding the route to the router.
    # __qualname__ is used for the NR transaction name.
    # __name__ is used
    #          - to create the openapi path summary
    #          - by generate_operation_id (operation_id is used by our SDK generator)
    function_name_override = f"invoke_action_{action_type}"
    invoke_action.__qualname__ = function_name_override
    invoke_action.__name__ = function_name_override

    router.add_api_route(
        path=f"/{{integration_id}}/actions/{action_type}",
        endpoint=invoke_action,
        methods=["POST"],
        response_class=Response,
        status_code=httpstatus.HTTP_202_ACCEPTED,
    )


# Create invoke action routes for all publicly invocable actions in INTEGRATION_ACTIONS_METADATA
for action in IntegrationAction.get_public_invocable_actions():
    if metadata := action.metadata:
        create_invoke_action_route(
            action.action_type,
            metadata.args_type,
        )

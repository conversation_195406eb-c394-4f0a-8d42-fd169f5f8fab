from apps.connectors.integrations.vendors.vendor import Vendors

from .base import BaseApiTestCase


class VendorTests(BaseApiTestCase):
    def test_get_meta(self):
        response = self.client.get(self._vendors_url() + "/meta")
        meta = response.json()
        self.assertIn("category", meta)

        category = min(meta["category"], key=lambda x: x["id"])
        self.assertEqual(category["id"], "asset_source")

    def test_get_vendors(self):
        response = self.client.get(self._vendors_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(
            len(resp),
            len([v for v in Vendors.get_all_vendors() if v.id != "vulncheck"]),
        )

    def test_get_vendor_versions(self):
        response = self.client.get(self._vendors_url() + "/microsoft/template_versions")
        self.assertEqual(response.status_code, 200)

    def test_get_hidden_technology_versions(self):
        response = self.client.get(self._vendors_url() + "/vulncheck/template_versions")
        self.assertEqual(response.status_code, 404)

    def test_get_vendor_versions_by_category(self):
        response = self.client.get(
            self._vendors_url()
            + "/microsoft/template_versions"
            + "?category_id=ot_security&category_id=identity_security"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        category_ids = {v["category_id"] for v in resp}
        self.assertEqual(len(category_ids), 2)
        self.assertIn("ot_security", category_ids)
        self.assertIn("identity_security", category_ids)

    def test_get_vendor_versions_by_module(self):
        response = self.client.get(
            self._vendors_url() + "/microsoft/template_versions" + "?module=assets:*"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        for v in resp:
            self.assertIn("assets:*", v["modules"])

    def test_get_vendor_connection_templates(self):
        """Test getting all connection templates for a vendor"""
        response = self.client.get(
            self._vendors_url() + "/microsoft/connection_templates"
        )
        self.assertEqual(response.status_code, 200)
        templates = response.json()
        self.assertIsInstance(templates, list)

        templates = [
            template for template in templates if template["id"] == "ms_aad_app"
        ]
        self.assertEqual(len(templates), 1)
        template = templates[0]
        self.assertSetEqual(
            set(template["config"]["properties"].keys()),
            {"client_id", "microsoft_cloud_platform", "tenant_id"},
        )

    def test_get_vendor_connection_template_not_found(self):
        """Test getting a non-existent connection template"""
        response = self.client.get(
            self._vendors_url() + "/microsoft/connection_templates/non_existent_id"
        )
        self.assertEqual(response.status_code, 404)

    def test_get_vendor_connection_template(self):
        """Test getting a specific connection template"""
        template_id = "ms_aad_app"
        response = self.client.get(
            f"{self._vendors_url()}/microsoft/connection_templates/{template_id}"
        )
        self.assertEqual(response.status_code, 200)
        template = response.json()

        # Verify it's the correct template
        self.assertEqual(template["id"], template_id)
        self.assertEqual(template["name"], "Microsoft")
        self.assertEqual(template["vendor_id"], "microsoft")

        # Verify schema structure
        self.assertSetEqual(
            set(template["config"]["properties"].keys()),
            {"client_id", "microsoft_cloud_platform", "tenant_id"},
        )

    def test_get_vendor_connection_templates_nonexistent_vendor(self):
        """Test getting connection templates for a vendor that doesn't exist"""
        response = self.client.get(
            self._vendors_url() + "/nonexistent_vendor/connection_templates"
        )
        self.assertEqual(response.status_code, 404)

        response = self.client.get(
            self._vendors_url() + "/nonexistent_vendor/connection_templates/template_id"
        )
        self.assertEqual(response.status_code, 404)

    def test_get_vendor_connection_templates_hidden_vendor(self):
        """Test getting connection templates for a vendor that is hidden"""
        response = self.client.get(
            self._vendors_url() + "/vulncheck/connection_templates"
        )
        self.assertEqual(response.status_code, 404)

    def _vendors_url(self):
        return "v1/vendors"

from uuid import uuid4

from fastapi import status

from apps.api.permissions import Permission
from apps.connectors.models import Connection
from apps.tests.base import BaseCaseMixin
from factories.aad_app import AadAppFactory
from factories.connection import ConnectionFactory

from .base import BaseApiTestCase


class ConnectionTests(BaseApiTestCase, BaseCaseMixin):
    def setUp(self):
        super().setUp()

        self._patch_encryption()

        self.aad_app = AadAppFactory()
        self.connection = ConnectionFactory(
            organization=self.organization,
            connection_template_id="ms_aad_app",
            config__client_id=self.aad_app.client_id,
        )

        self.set_org_permissions(self.organization, {Permission.MANAGE_INTEGRATIONS})

    def test_get_connections(self):
        response = self.client.get(self._connections_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        connection_summary = min(resp, key=lambda x: x["connection_template_id"])
        self.assertEqual(connection_summary["connection_template_id"], "ms_aad_app")
        self.assertEqual(connection_summary["vendor_id"], "microsoft")

    def test_get_connection(self):
        response = self.client.get(
            self._connections_url() + "/" + str(self.connection.id)
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        connection = resp
        self.assertEqual(connection["connection_template_id"], "ms_aad_app")
        self.assertEqual(connection["vendor_id"], "microsoft")

    def test_create_connection(self):
        connection = {
            "connection_template_id": "ms_aad_app",
            "config": {
                "tenant_id": str(uuid4()),
                "client_id": self.aad_app.client_id,
            },
            "organization_id": str(self.organization.id),
        }
        response = self.client.post(self._connections_url(), json=connection)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        connection = resp
        self.assertEqual(connection["connection_template_id"], "ms_aad_app")
        self.assertEqual(connection["config"]["client_id"], self.aad_app.client_id)
        self.assertEqual(connection["vendor_id"], "microsoft")

    def test_update_connection(self):
        expected = str(uuid4())
        new_aad_app = AadAppFactory(client_id=expected, organization=self.organization)
        connection = {
            "connection_template_id": "ms_aad_app",
            "config": {
                "tenant_id": str(uuid4()),
                "client_id": new_aad_app.client_id,
            },
            "organization_id": str(self.organization.id),
        }
        response = self.client.put(
            self._connections_url() + "/" + str(self.connection.id), json=connection
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        connection = resp
        self.assertEqual(connection["connection_template_id"], "ms_aad_app")
        self.assertEqual(connection["config"]["client_id"], expected)
        self.assertEqual(connection["vendor_id"], "microsoft")

        connection_obj = Connection.objects.get(id=connection["id"])
        self.assertEqual(connection_obj.config["client_id"], expected)

    def test_update_connection_invalid_aad_app(self):
        invalid_client_id = str(uuid4())
        connection = {
            "connection_template_id": "ms_aad_app",
            "config": {
                "tenant_id": str(uuid4()),
                "client_id": invalid_client_id,  # Invalid client_id
            },
            "organization_id": str(self.organization.id),
        }
        response = self.client.put(
            self._connections_url() + "/" + str(self.connection.id), json=connection
        )
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        resp = response.json()
        self.assertIn(invalid_client_id, resp["error"]["message"])

    def test_delete_connection(self):
        response = self.client.delete(
            self._connections_url() + "/" + str(self.connection.id)
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        connection = resp

        connection = Connection.objects.get(id=connection["id"])
        self.assertEqual(connection.connection_template_id, "ms_aad_app")
        self.assertEqual(connection.is_deleted, True)

        # assert that the connection is not returned in the list of connections
        response = self.client.get(self._connections_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 0)

    def test_get_connection_not_exists(self):
        response = self.client.get(self._connections_url() + "/" + str(uuid4()))
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def _connections_url(self):
        return "v1/connections"

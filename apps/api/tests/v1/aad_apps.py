from uuid import uuid4

from ata_common.services import encryption_service

from apps.connectors.integrations.types import EncryptedStr
from factories import AadAppFactory

from .base import BaseApiTestCase


class AadAppTests(BaseApiTestCase):
    def setUp(self):
        super().setUp()

        self.aad_app_root = AadAppFactory()
        self.aad_app_other = AadAppFactory(organization=self.child_organization)
        self.aad_app = AadAppFactory(organization=self.organization)

        self._patch_encryption()

        # Grant manage integrations permission for the test org
        self.set_org_permissions(
            self.organization, ["data_connectors:manage_integrations"]
        )

    def test_get_global_aad_app(self):
        response = self.client.get(
            self._aad_apps_url() + "/" + str(self.aad_app_root.client_id)
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json(),
            {
                "client_id": str(self.aad_app_root.client_id),
                "name": self.aad_app_root.name,
                "organization_id": None,
            },
        )

    def test_list_aad_apps(self):
        response = self.client.get(self._aad_apps_url())
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json(),
            {
                "items": [
                    {
                        "client_id": str(self.aad_app.client_id),
                        "name": self.aad_app.name,
                        "organization_id": str(self.organization.id),
                    },
                    {
                        "client_id": str(self.aad_app_root.client_id),
                        "name": self.aad_app_root.name,
                        "organization_id": None,
                    },
                ]
            },
        )

    def test_create_aad_app(self):
        client_id = str(uuid4())
        response = self.client.post(
            self._aad_apps_url(),
            json={
                "client_id": client_id,
                "client_secret": "test_secret",
                "name": "test_name",
            },
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json(),
            {
                "client_id": client_id,
                "name": "test_name",
                "organization_id": str(self.organization.id),
            },
        )

    def test_update_aad_app(self):
        response = self.client.put(
            self._aad_apps_url() + "/" + str(self.aad_app.client_id),
            json={
                "client_secret": "changed_secret",
                "name": "new_name",
            },
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json(),
            {
                "client_id": str(self.aad_app.client_id),
                "name": "new_name",
                "organization_id": str(self.organization.id),
            },
        )
        self.aad_app.refresh_from_db()
        decrypted = encryption_service.decrypt(self.aad_app.client_secret)
        self.assertEqual(decrypted, "changed_secret")

    def test_update_aad_app_no_secret_change(self):
        response = self.client.put(
            self._aad_apps_url() + "/" + str(self.aad_app.client_id),
            json={
                "client_secret": EncryptedStr.MASK,
                "name": "new_name",
            },
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json(),
            {
                "client_id": str(self.aad_app.client_id),
                "name": "new_name",
                "organization_id": str(self.organization.id),
            },
        )

    def test_update_aad_app_other_org_fails(self):
        response = self.client.put(
            self._aad_apps_url() + "/" + str(self.aad_app_root.client_id),
            json={
                "name": "new_name",
            },
        )
        self.assertEqual(response.status_code, 404)
        response = self.client.put(
            self._aad_apps_url() + "/" + str(self.aad_app_other.client_id),
            json={
                "name": "new_name",
            },
        )
        self.assertEqual(response.status_code, 404)

    def test_update_aad_app_name_only(self):
        expected_secret = self.aad_app.client_secret
        response = self.client.put(
            self._aad_apps_url() + "/" + str(self.aad_app.client_id),
            json={
                "name": "new_name",
            },
        )
        self.assertEqual(response.status_code, 200)
        self.aad_app.refresh_from_db()
        self.assertEqual(self.aad_app.client_secret, expected_secret)
        self.assertEqual(self.aad_app.name, "new_name")

    def test_update_aad_app_secret_only(self):
        expected_name = self.aad_app.name
        response = self.client.put(
            self._aad_apps_url() + "/" + str(self.aad_app.client_id),
            json={
                "client_secret": "new_secret",
            },
        )
        self.assertEqual(response.status_code, 200)
        self.aad_app.refresh_from_db()
        decrypted = encryption_service.decrypt(self.aad_app.client_secret)
        self.assertEqual(decrypted, "new_secret")
        self.assertEqual(self.aad_app.name, expected_name)

    def test_delete_aad_app(self):
        response = self.client.delete(
            self._aad_apps_url() + "/" + str(self.aad_app.client_id)
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"detail": "Deleted"})

    def test_delete_global_app_fails(self):
        response = self.client.delete(
            self._aad_apps_url() + "/" + str(self.aad_app_root.client_id)
        )
        self.assertEqual(response.status_code, 404)

    @staticmethod
    def _aad_apps_url():
        return "v1/aad_apps"

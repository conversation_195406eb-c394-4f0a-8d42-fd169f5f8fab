import responses
from criticalstart.auth.v2.test import factory
from criticalstart.auth.v2.test.fastapi.sessions import UserSessionOverride
from django.test import TransactionTestCase
from fastapi.testclient import TestClient

from apps.api.deps import session
from apps.tests.base import BaseCaseMixin
from core.asgi import app
from factories import AccountFactory, OrganizationFactory


class BaseApiTestCase(TransactionTestCase, BaseCaseMixin):
    maxDiff = None

    def setUp(self):
        super().setUp()
        account = AccountFactory()
        self.organization = OrganizationFactory(
            alias="test_organization1", account=account
        )
        self.child_organization = OrganizationFactory(
            alias="test_organization2", account=account
        )

        self.client = TestClient(
            app,
            headers={
                "Authorization": "Bearer test_token",
                "X-CS-Organization": str(self.organization.id),
            },
            base_url="http://testserver/api",
        )

        model = factory.UserSession(
            user__email="<EMAIL>",
            organizations=[
                factory.OrganizationUserPermissions(
                    organization=factory.Organization(
                        id=self.organization.id,
                        alias=self.organization.alias,
                        account_id=self.organization.account.id,
                        account_alias=self.organization.account.alias,
                    )
                ),
                factory.OrganizationUserPermissions(
                    organization=factory.Organization(
                        id=self.child_organization.id,
                        alias=self.child_organization.alias,
                        account_id=self.child_organization.account.id,
                        account_alias=self.child_organization.account.alias,
                    )
                ),
            ],
        )
        self.session = UserSessionOverride(
            app=self.client.app, dependency=session, model=model
        )
        self.session.start()
        responses.mock.reset()
        responses.mock.start()

    def tearDown(self):
        super().tearDown()
        self.client = None
        self.session.stop()
        responses.mock.stop()
        responses.mock.reset()

    def set_org_permissions(self, organization, permissions):
        for org in self.session.model.organizations:
            if org.organization.id == organization.id:
                org.permissions = set(permissions)
                return
        raise Exception(
            "No organization found with id {}".format(organization.id)
        )  # pragma: no cover

from datetime import datetime
from unittest.mock import patch
from uuid import uuid4

import pytest
import responses
from django.utils import timezone
from fastapi import status

from apps.api.permissions import Permission
from apps.api.v1.schemas.integration import CoverageMode
from apps.connectors.admin import connector_admin
from apps.connectors.integrations.types import EncryptedStr
from apps.connectors.integrations.vendors.tenable.tenable_io.template import (
    TenableIoV1TemplateVersion,
)
from apps.connectors.models import AadApp, Connector
from apps.connectors.services import connector_service
from apps.connectors.tests.integrations.azure_ad_v1 import setup_oauth_responses
from apps.tests.base import BaseCaseMixin
from factories import (
    AadAppFactory,
    ActivityLogFactory,
    ConnectorFactory,
    OrganizationFactory,
)
from factories.connection import ConnectionFactory

from .base import BaseApiTestCase


class IntegrationTests(BaseApiTestCase, BaseCaseMixin):
    def setUp(self):
        super().setUp()

        self._patch_encryption()

        self.connector = ConnectorFactory(
            organization=self.organization,
            technology_id="tenable_io",
            version_id="v1",
            last_activity_at=timezone.now(),
            settings={
                "host_sync": {
                    "fetch_from_last_x_days": 10,
                    "fetch_from_unauthenticated_scans": True,
                }
            },
        )

        self.connector_falcon = ConnectorFactory(
            organization=self.organization,
            technology_id="falcon_em",
            version_id="v1",
            last_activity_at=timezone.now(),
            settings={
                "host_sync": {
                    "fetch_from_last_x_days": 10,
                    "fetch_from_unauthenticated_scans": True,
                }
            },
            enabled_actions=[
                "detected_vulnerability_sync",
                "vendor_vulnerability_sync",
                "vulnerability_intelligence_sync",
            ],
            vulnerability_coverage_mode="enabled",
            endpoint_coverage_mode="n/a",
        )

        self.connector_falcon_1 = ConnectorFactory(
            organization=self.organization,
            technology_id="falcon_em",
            version_id="v1",
            last_activity_at=timezone.now(),
            settings={
                "host_sync": {
                    "fetch_from_last_x_days": 10,
                    "fetch_from_unauthenticated_scans": True,
                }
            },
            enabled_actions=["vendor_vulnerability_sync"],
            vulnerability_coverage_mode="enabled",
            endpoint_coverage_mode="n/a",
        )

        self.set_org_permissions(self.organization, {Permission.MANAGE_INTEGRATIONS})

    def _setup_additional_connectors(self):
        aad_app = AadAppFactory()
        self.connector_azure_ad = ConnectorFactory(
            organization=self.organization,
            technology_id="azure_ad",
            version_id="v1",
            last_activity_at=timezone.now(),
            config__client_id=aad_app.client_id,
        )
        self.connector_defender_atp = ConnectorFactory(
            organization=self.organization,
            technology_id="defender_atp",
            version_id="v1",
            last_activity_at=timezone.now(),
            config__client_id=aad_app.client_id,
        )
        self.connector_ms_mde = ConnectorFactory(
            organization=self.organization,
            technology_id="ms_mde",
            version_id="v1",
            last_activity_at=timezone.now(),
            config__client_id=aad_app.client_id,
        )
        self.connector_defender_atp_1 = ConnectorFactory(
            organization=self.organization,
            technology_id="defender_atp",
            version_id="v1",
            last_activity_at=timezone.now(),
            config__client_id=aad_app.client_id,
        )

    def get_meta(self):
        response = self.client.get(self._integrations_url() + "/meta")
        self.assertEqual(200, response.status_code, response.json())
        return response

    def test_get_meta(self):
        response = self.get_meta()
        meta = response.json()
        self.assertIn("status", meta)
        status = min(meta["status"], key=lambda x: x["id"])
        self.assertEqual(status["id"], "healthy")

    def test_get_integrations(self):
        response = self.client.get(self._integrations_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 3)
        integration_summary = min(resp, key=lambda x: x["technology_id"])
        self.assertEqual(
            connector_admin.ConnectorAdmin.get_artifact_path(
                self, self.connector_falcon
            ),
            f"<a href=organization_id={self.organization.id}/technology_id=falcon_em/v1/connector_id={self.connector_falcon.id}>Artifact Path</a>",
        )

        self.assertTrue(integration_summary["enabled"])
        self.assertEqual(integration_summary["technology_id"], "falcon_em")
        self.assertEqual(integration_summary["version_id"], "v1")
        self.assertEqual(integration_summary["category_name"], "Asset Sources")
        self.assertEqual(
            self.parse_datetime(integration_summary["last_activity_at"]),
            self.connector_falcon.last_activity_at,
        )
        self.assertEqual(integration_summary["vulnerability_coverage_mode"], "enabled")
        self.assertEqual(integration_summary["endpoint_coverage_mode"], "n/a")

    def test_get_integrations_with_technology_id(self):
        response = self.client.get(
            self._integrations_url() + "?technology_id=tenable_io"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        integration_summary = min(resp, key=lambda x: x["technology_id"])
        self.assertEqual(integration_summary["technology_id"], "tenable_io")

        response = self.client.get(
            self._integrations_url()
            + "?technology_id=tenable_io&technology_id=falcon_em"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 3)
        self.assertEqual(
            sorted({x["technology_id"] for x in resp}), ["falcon_em", "tenable_io"]
        )

    def test_get_integrations_with_hidden_technology_id(self):
        ConnectorFactory(
            organization=self.organization, technology_id="vulncheck", version_id="v1"
        )

        response = self.client.get(
            self._integrations_url() + "?technology_id=vulncheck"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 0)

    def test_get_integrations_with_technology_id_and_category_id(self):
        response = self.client.get(
            self._integrations_url()
            + "?technology_id=falcon_em&category_id=identity_security"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 0)

        response = self.client.get(
            self._integrations_url()
            + "?technology_id=falcon_em&category_id=asset_source"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 2)

    def test_get_integrations_with_category_id(self):
        self._setup_additional_connectors()

        response = self.client.get(
            self._integrations_url() + "?category_id=identity_security"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        integration_summary = min(resp, key=lambda x: x["category_id"])
        self.assertEqual(integration_summary["category_id"], "identity_security")

        response = self.client.get(
            self._integrations_url() + "?category_id=vulnerability_management"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        integration_summary = min(resp, key=lambda x: x["category_id"])
        self.assertEqual(integration_summary["category_id"], "vulnerability_management")

        response = self.client.get(
            self._integrations_url()
            + "?category_id=identity_security&category_id=vulnerability_management"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 2)

    def test_get_integrations_with_vendor_id(self):
        self._setup_additional_connectors()

        response = self.client.get(self._integrations_url() + "?vendor_id=microsoft")
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 4)
        integration_summary = min(resp, key=lambda x: x["vendor_id"])
        self.assertEqual(integration_summary["vendor_id"], "microsoft")

        response = self.client.get(self._integrations_url() + "?vendor_id=crowdstrike")
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 2)
        integration_summary = min(resp, key=lambda x: x["vendor_id"])
        self.assertEqual(integration_summary["vendor_id"], "crowdstrike")

        response = self.client.get(
            self._integrations_url() + "?vendor_id=microsoft&vendor_id=crowdstrike"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 6)

    def test_get_integrations_with_category_id_and_vendor_id(self):
        self._setup_additional_connectors()

        response = self.client.get(
            self._integrations_url()
            + "?category_id=identity_security&vendor_id=microsoft&vendor_id=crowdstrike"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        integration_summary = min(resp, key=lambda x: x["category_id"])
        self.assertEqual(integration_summary["category_id"], "identity_security")
        self.assertEqual(integration_summary["vendor_id"], "microsoft")

        response = self.client.get(
            self._integrations_url()
            + "?category_id=identity_security&category_id=endpoint_security&vendor_id=microsoft&vendor_id=crowdstrike"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 4)

    def test_get_integrations_with_module(self):
        self._setup_additional_connectors()

        response = self.client.get(self._integrations_url() + "?module=vulnerability:*")
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        self.assertIn("vulnerability:*", resp[0]["modules"])

        response = self.client.get(self._integrations_url() + "?module=mdr:*")
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 2)
        self.assertEqual(
            sorted([x["technology_id"] for x in resp]), ["azure_ad", "ms_mde"]
        )

        response = self.client.get(
            self._integrations_url() + "?module=vulnerability:*&module=mdr:*"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 3)

    def test_get_integrations_with_status(self):
        self._setup_additional_connectors()

        # First, test when no health check has been refreshed
        response = self.client.get(self._integrations_url() + "?status=unknown")
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 7)
        integration_summary = min(resp, key=lambda x: x["status"])
        self.assertEqual(integration_summary["status"], "unknown")

        setup_oauth_responses(self.connector_azure_ad.config["tenant_id"])

        # Refresh the health check for one connector
        url = "/".join(
            [
                self._integrations_url(),
                str(self.connector_azure_ad.id),
                "refresh_health_checks",
            ]
        )
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)

        # Retest the status filter
        response = self.client.get(self._integrations_url() + "?status=healthy")
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        self.assertEqual(resp[0]["id"], str(self.connector_azure_ad.id))

        response = self.client.get(
            self._integrations_url() + "?status=healthy&status=unknown"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 7)

    def test_get_integrations_with_enabled_actions(self):
        response = self.client.get(
            self._integrations_url() + "?enabled_action=detected_vulnerability_sync"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        resp = resp[0]
        self.assertEqual(resp["technology_id"], "falcon_em")
        self.assertEqual(
            resp["enabled_actions"],
            [
                "detected_vulnerability_sync",
                "vendor_vulnerability_sync",
                "vulnerability_intelligence_sync",
            ],
        )

    def test_get_integrations_with_multiple_enabled_actions(self):
        response = self.client.get(
            self._integrations_url()
            + "?enabled_action=vendor_vulnerability_sync&enabled_action=detected_vulnerability_sync"
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 2)
        resp = resp[0]
        self.assertEqual(resp["technology_id"], "falcon_em")
        self.assertTrue(
            "vendor_vulnerability_sync" in resp["enabled_actions"]
            or "detected_vulnerability_sync" in resp["enabled_actions"]
        )

    def test_get_integrations_empty_child_org(self):
        response = self.client.get(
            self._integrations_url(),
            headers={"X-CS-Organization": str(self.child_organization.id)},
        )
        self.assertEqual(response.status_code, 200, "user is authorized")
        resp = response.json()["items"]
        self.assertEqual(len(resp), 0, "child does not have any integrations")

    def test_get_integrations_unauthorized_org(self):
        # User is authorized for test_organization1
        response = self.client.get(
            self._integrations_url(),
            headers={"X-CS-Organization": str(self.organization.id)},
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # User is not authorized for an Org outside of test_organization1
        response = self.client.get(
            self._integrations_url(),
            headers={"X-CS-Organization": "00000000-0000-0000-0000-000000000000"},
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_get_integration(self):
        response = self.client.get(
            self._integrations_url() + "/" + str(self.connector.id)
        )
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "tenable_io")
        self.assertEqual(integration["version_id"], "v1")
        self.assertEqual(integration["category_name"], "Vulnerability Management")
        self.assertEqual(integration["config"]["secret_key"], "**********")
        self.assertEqual(
            integration["settings"],
            {
                "host_sync": {
                    "fetch_from_last_x_days": 10,
                    "fetch_from_unauthenticated_scans": True,
                }
            },
        )
        self.assertEqual(
            self.parse_datetime(integration["last_activity_at"]),
            self.connector.last_activity_at,
        )

    def test_get_integration_wrong_context(self):
        response = self.client.get(
            self._integrations_url() + "/" + str(self.connector.id),
            headers={"X-CS-Organization": str(self.child_organization.id)},
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_internal_integration(self):
        internal_connector = ConnectorFactory(
            technology_id="azure_ad",
            version_id="v1",
            organization=self.organization,
            internal=True,
            settings={"confirm_risky_user": {}},
        )
        response = self.client.get(
            self._integrations_url() + "/" + str(internal_connector.id)
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_integration_not_exists(self):
        response = self.client.get(self._integrations_url() + "/" + str(uuid4()))
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_create_integration_with_connection(self):
        aad_app = AadAppFactory()
        connection = ConnectionFactory(
            organization=self.organization,
            connection_template_id="ms_aad_app",
            config__client_id=aad_app.client_id,
        )
        integration = {
            "technology_id": "ms_xdr",
            "version_id": "v1",
            "settings": {},
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
            "connection_id": str(connection.id),
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "ms_xdr")
        self.assertEqual(integration["version_id"], "v1")
        self.assertEqual(integration["connection_id"], str(connection.id))
        self.assertEqual(integration["config"]["client_id"], aad_app.client_id)

    def test_create_integration(self):
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "test_access_key",
                "secret_key": "test_secret_key",
            },
            "settings": {
                "host_sync": {"fetch_by_tags": "Category:tag1, Category:tag2"}
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "tenable_io")
        self.assertEqual(integration["version_id"], "v1")
        self.assertEqual(integration["category_name"], "Vulnerability Management")
        self.assertEqual(integration["config"]["secret_key"], "**********")
        self.assertEqual(
            integration["settings"],
            {
                "host_sync": {
                    "fetch_from_last_x_days": 30,
                    "fetch_from_unauthenticated_scans": True,
                    "fetch_unlicensed_hosts": True,
                    "fetch_by_tags": "Category:tag1, Category:tag2",
                    "fetch_by_scan_ids": "",
                },
            },
        )
        self.assertCountEqual(integration["enabled_actions"], ["host_sync"])
        self.assertIsNone(integration["last_activity_at"])
        self.assertEqual(integration["vulnerability_coverage_mode"], "enabled")
        self.assertEqual(integration["endpoint_coverage_mode"], "n/a")

        # assert activity log is created
        last_log = Connector.objects.get(id=integration["id"]).activity_logs.last()
        self.assertEqual(last_log.message, "Integration created")

    @responses.activate
    def test_create_palo_alto_integration(self):
        responses.post(
            "https://test_base_url.com/api/?type=keygen",
            status=201,
            body="""
        <response status="success">
            <result>
                <key>dummy_key</key>
            </result>
        </response>""",
        )
        integration = {
            "technology_id": "palo_alto_panorama",
            "version_id": "v1",
            "config": {
                "base_url": "https://test_base_url.com",
                "username": "test_secret_key",
                "password": "test_password",
                "api_key": "",
                "verify_tls": True,
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
            "settings": {
                "block_ip_firewall": {"dynamic_address_group": "malware"},
                "unblock_ip_firewall": {"dynamic_address_group": "malware"},
            },
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "palo_alto_panorama")
        self.assertEqual(integration["version_id"], "v1")
        self.assertEqual(integration["category_name"], "Network Security")
        self.assertEqual(integration["config"]["password"], "**********")
        self.assertEqual(integration["config"]["api_key"], "**********")

        # assert activity log is created
        last_log = Connector.objects.get(id=integration["id"]).activity_logs.last()
        self.assertEqual(last_log.message, "Integration created")

    @responses.activate
    def test_create_palo_alto_integration_fail(self):
        responses.post(
            "https://test_base_url.com/api/?type=keygen",
            status=400,
            body="""
        <response status="success">
            <result>
            </result>
        </response>""",
        )
        integration = {
            "technology_id": "palo_alto_panorama",
            "version_id": "v1",
            "config": {
                "base_url": "https://test_base_url.com",
                "username": "test_secret_key",
                "password": "test_password",
                "api_key": "",
            },
            "settings": {
                "block_ip_firewall": {"dynamic_address_group": "malware"},
                "unblock_ip_firewall": {"dynamic_address_group": "malware"},
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        error = response.json()["error"]
        self.assertIn("API key could not be generated", error["message"])

    def test_create_integration_no_permissions(self):
        self.set_org_permissions(self.organization, set())
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "test_access_key",
                "secret_key": "test_secret_key",
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_integration_default_settings(self):
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "test_access_key",
                "secret_key": "test_secret_key",
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(
            integration["settings"],
            {
                "host_sync": {
                    "fetch_from_last_x_days": 30,
                    "fetch_from_unauthenticated_scans": True,
                    "fetch_unlicensed_hosts": True,
                    "fetch_by_tags": "",
                    "fetch_by_scan_ids": "",
                },
            },
        )
        self.assertEqual(
            integration["settings"], TenableIoV1TemplateVersion.default_settings()
        )

    def test_create_hidden_integration(self):
        integration = {
            "technology_id": "vulncheck",
            "version_id": "v1",
            "config": {"token": "test_token"},
            "settings": {},
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_create_integration_aad_app_not_exists(self):
        client_id = str(uuid4())
        tenant_id = str(uuid4())
        integration = {
            "technology_id": "azure_ad",
            "version_id": "v1",
            "config": {
                "tenant_id": tenant_id,
                "client_id": client_id,
            },
            "settings": {
                "host_sync": {
                    "device_group_blocklist": ["group1", "group2"],
                    "fetch_joined_devices": False,
                }
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.assertIn(client_id, response.json()["error"]["message"])

    def _prepare_aad_integration(self):
        client_id = str(uuid4())
        tenant_id = str(uuid4())
        AadApp.objects.create(
            client_id=client_id,
            client_secret="test_client_secret",
        )
        integration = {
            "technology_id": "azure_ad",
            "version_id": "v1",
            "config": {
                "tenant_id": tenant_id,
                "client_id": client_id,
            },
            "settings": {
                "host_sync": {
                    "device_group_blocklist": ["group1", "group2"],
                    "fetch_joined_devices": False,
                }
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
        }
        return integration, tenant_id, client_id

    def test_create_integration_aad_app_exists(self):
        integration, tenant_id, client_id = self._prepare_aad_integration()
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        integration = response.json()
        self.assertEqual(integration["technology_id"], "azure_ad")
        self.assertEqual(integration["version_id"], "v1")
        self.assertEqual(integration["category_name"], "Identity Security")
        self.assertEqual(integration["config"]["tenant_id"], tenant_id)
        self.assertEqual(integration["config"]["client_id"], client_id)
        self.assertEqual(
            integration["settings"],
            {
                "host_sync": {
                    "fetch_from_last_x_days": 30,
                    "fetch_disabled_devices": True,
                    "fetch_entra_joined_devices": True,
                    "include_device_owner": True,
                    "include_device_groups": True,
                    "fetch_by_groups": "",
                    "custom_filter_expression": "",
                },
            },
        )

    def test_create_integration_connection_not_exists(self):
        # Create a Connection for a technology with a different organization
        organization = OrganizationFactory()
        connection = ConnectionFactory(
            organization=organization,
            connection_template_id="tenable_io",
            config__access_key="test_access_key",
            config__secret_key="test_secret_key",
        )

        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "connection_id": str(connection.id),
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_create_integration_invalid_connection_config(self):
        # Create a Connection for a technology with a different config
        aad_app = AadAppFactory()
        connection = ConnectionFactory(
            organization=self.organization,
            connection_template_id="ms_aad_app",
            config__client_id=aad_app.client_id,
        )

        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "connection_id": str(connection.id),
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
        }
        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)

    def test_integration_validate_invalid(self):
        integration, tenant_id, client_id = self._prepare_aad_integration()

        responses.add_passthru("https://login.microsoftonline.com")
        response = self.client.post(
            self._integrations_url() + "/validate", json=integration
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        integration = response.json()
        validation = integration["validation"]

        self.assertTrue(validation)

        component = validation[0]
        self.assertEqual(component["id"], "critical-config-valid")

        requirement = component["requirements"][0]
        self.assertEqual(requirement["status"], "failed")

    def test_integration_validate_valid(self):
        integration, tenant_id, client_id = self._prepare_aad_integration()

        setup_oauth_responses(tenant_id)

        response = self.client.post(
            self._integrations_url() + "/validate", json=integration
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        integration = response.json()
        validation = integration["validation"]

        self.assertTrue(validation)

        component = validation[0]
        self.assertEqual(component["id"], "critical-config-valid")

        requirement = component["requirements"][0]
        self.assertEqual(requirement["status"], "passed")

    def test_update_integration(self):
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "updated_access_key",
                "secret_key": "updated_secret_key",
                "url": "updated_url",
            },
            "settings": {"host_sync": {"fetch_from_last_x_days": 31}},
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
        }
        integration = self.put(self.connector.id, json=integration)

        self.assertEqual(integration["technology_id"], "tenable_io")
        self.assertEqual(integration["version_id"], "v1")
        self.assertEqual(integration["category_name"], "Vulnerability Management")
        self.assertEqual(integration["config"]["secret_key"], "**********")
        self.assertTrue(integration["enabled"])
        self.assertEqual(integration["enabled_actions"], [])

        connector = Connector.objects.get(id=integration["id"])
        self.assertEqual(connector.config["access_key"], "updated_access_key")
        self.assertEqual(connector.config["secret_key"], "updated_secret_key")
        self.assertEqual(
            connector.settings,
            {
                "host_sync": {
                    "fetch_from_last_x_days": 31,
                    "fetch_from_unauthenticated_scans": True,
                    "fetch_unlicensed_hosts": True,
                    "fetch_by_tags": "",
                    "fetch_by_scan_ids": "",
                },
            },
        )

        self.assert_activity_logs(
            connector.id,
            [
                """Setting 'Fetch hosts' > 'Fetch hosts updated in the last X days' changed from 10 to 31 by <EMAIL>""",
                """Configuration 'Secret Key' changed from \"********\" to \"********\" by <EMAIL>""",
                """Configuration 'Access Key' changed from \"********\" to \"********\" by <EMAIL>""",
                """Configuration 'Tenable URL Override' changed from \"https://cloud.tenable.com\" to \"updated_url\" by <EMAIL>""",
            ],
        )

    def test_update_integration_no_permissions(self):
        self.set_org_permissions(self.organization, set())
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "updated_access_key",
                "secret_key": "updated_secret_key",
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
        }
        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_integration_two_settings_activity_logs(self):
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": self.connector.config,
            "settings": {
                "host_sync": {
                    "fetch_from_last_x_days": 45,
                    "fetch_from_unauthenticated_scans": False,
                }
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
        }
        self.put(self.connector.id, json=integration)

        self.assert_activity_logs(
            self.connector.id,
            [
                """Setting 'Fetch hosts' > 'Fetch hosts from unauthenticated scans' changed from enabled to <NAME_EMAIL>""",
                """Setting 'Fetch hosts' > 'Fetch hosts updated in the last X days' changed from 10 to 45 by <EMAIL>""",
            ],
        )

    def test_update_integration_enabled_actions_activity_logs(self):
        integration = self.get(connector_id=self.connector.id)

        expected_messages = []
        self.assert_activity_logs(self.connector.id, expected_messages)

        integration["enabled_actions"] = ["host_sync"]
        self.put(self.connector.id, json=integration)
        msg = "'Fetch hosts' changed from disabled to <NAME_EMAIL>"
        expected_messages.insert(0, msg)
        self.assert_activity_logs(self.connector.id, expected_messages)

        integration["enabled_actions"] = ["detected_vulnerability_sync", "host_sync"]
        self.put(self.connector.id, json=integration)
        msg = "'Fetch detected vulnerabilities' changed from disabled to <NAME_EMAIL>"
        expected_messages.insert(0, msg)
        self.assert_activity_logs(self.connector.id, expected_messages)

        integration["enabled_actions"] = ["detected_vulnerability_sync"]
        self.put(self.connector.id, json=integration)
        msg = "'Fetch hosts' changed from enabled to <NAME_EMAIL>"
        expected_messages.insert(0, msg)
        self.assert_activity_logs(self.connector.id, expected_messages)

    @pytest.mark.skip(
        reason="Test is currently failing due to defaults witting over previously created settings. "
        "We need to determine if/how we handle persisting default settings."
    )
    def test_update_integration_settings(self):
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "updated_access_key",
                "secret_key": "updated_secret_key",
            },
            "settings": {
                "host_sync": {
                    "fetch_from_last_x_days": 31,
                    "fetch_from_unauthenticated_scans": False,
                }
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
        }
        self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )

        connector = Connector.objects.get(id=self.connector.id)
        self.assertEqual(
            connector.settings,
            {
                "host_sync": {
                    "fetch_from_last_x_days": 31,
                    "fetch_from_unauthenticated_scans": False,
                    "fetch_unlicensed_hosts": True,
                    "fetch_by_tags": "",
                    "fetch_by_scan_ids": "",
                },
                "detected_vulnerability_sync": {},
                "vendor_vulnerability_sync": {},
            },
        )

        integration["settings"] = {
            "host_sync": {
                "fetch_unlicensed_hosts": False,
            }
        }
        self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )

        connector = Connector.objects.get(id=self.connector.id)
        self.assertEqual(
            connector.settings,
            {
                "host_sync": {
                    "fetch_from_last_x_days": 31,
                    "fetch_from_unauthenticated_scans": False,
                    "fetch_unlicensed_hosts": False,
                    "fetch_by_tags": "",
                    "fetch_by_scan_ids": "",
                },
                "detected_vulnerability_sync": {},
                "vendor_vulnerability_sync": {},
            },
        )

    def test_update_integration_name(self):
        integration = self.client.get(
            self._integrations_url() + "/" + str(self.connector.id)
        ).json()

        integration["name"] = "updated_name"

        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["name"], "updated_name")

        connector = Connector.objects.get(id=integration["id"])
        self.assertEqual(connector.name, "updated_name")

        self.assert_activity_logs(
            connector.id,
            [
                """Name changed from \"\" to \"updated_name\" by <EMAIL>""",
            ],
        )

    def test_update_integration_without_name(self):
        self.connector.name = "test_name"
        self.connector.save()

        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "updated_access_key",
                "secret_key": "updated_secret_key",
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
        }
        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["name"], "test_name")

    def test_update_integration_no_secret_change(self):
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "test_access_key",
                "secret_key": EncryptedStr.MASK,
            },
            "settings": {},
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
        }
        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "tenable_io")
        self.assertEqual(integration["version_id"], "v1")
        self.assertEqual(integration["category_name"], "Vulnerability Management")
        self.assertEqual(integration["config"]["secret_key"], "**********")
        self.assertEqual(
            integration["settings"],
            {
                "host_sync": {
                    "fetch_from_last_x_days": 30,
                    "fetch_from_unauthenticated_scans": True,
                    "fetch_unlicensed_hosts": True,
                    "fetch_by_tags": "",
                    "fetch_by_scan_ids": "",
                },
            },
        )
        self.assertEqual(integration["enabled_actions"], [])

        connector = Connector.objects.get(id=integration["id"])
        self.assertEqual(connector.config["secret_key"], "test_secret_key")

    def test_update_integration_disabled(self):
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "test_access_key",
                "secret_key": EncryptedStr.MASK,
            },
            "settings": self.connector.settings,
            "organization_id": str(self.organization.id),
            "enabled": False,
            "enabled_actions": [],
        }

        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertFalse(integration["enabled"])

        self.assert_activity_logs(
            integration["id"],
            ["Enabled changed from enabled to <NAME_EMAIL>"],
        )

    def test_update_integration_missing_enabled(self):
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "test_access_key",
                "secret_key": EncryptedStr.MASK,
            },
            "settings": {},
            "organization_id": str(self.organization.id),
            "enabled_actions": [],  # should result in enabled being set to False
        }

        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertFalse(integration["enabled"])

        # Test the opposite
        integration.pop("enabled")
        integration["enabled_actions"] = [
            "host_sync"
        ]  # should result in enabled being set to True

        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp
        self.assertTrue(integration["enabled"])

    def test_update_integration_update_at_changed(self):
        now = timezone.now()
        before_updated_at = self.connector.updated_at

        integration = self.client.get(
            self._integrations_url() + "/" + str(self.connector.id)
        ).json()

        integration["name"] = "updated_name"

        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id), json=integration
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        connector = Connector.objects.get(id=integration["id"])
        self.assertLess(before_updated_at, connector.updated_at)
        self.assertLess(now, connector.updated_at)

    def test_update_integration_config_managed_by_portal(self):
        """Test that config does not change if config_managed_by_portal is True"""

        # Step 1: Create an integration with config_managed_by_portal = True
        self.connector.config_managed_by_portal = True
        self.connector.product_id = 1000
        self.connector.config = {
            "access_key": "original_access_key",
            "secret_key": "original_secret_key",
        }
        self.connector.save()

        # Step 2: Try to update config
        integration_update = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "updated_access_key",
                "secret_key": "updated_secret_key",
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": [],
        }

        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id),
            json=integration_update,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(self.connector.product_id, response_data["product_id"])
        self.assertEqual(
            self.connector.config_managed_by_portal,
            response_data["config_managed_by_portal"],
        )
        updated_connector = Connector.objects.get(id=self.connector.id)

        # Step 3: Assert that config did NOT change
        self.assertEqual(updated_connector.config["access_key"], "original_access_key")
        self.assertEqual(updated_connector.config["secret_key"], "original_secret_key")

        # Step 4: Set config_managed_by_portal = False and try again
        self.connector.config_managed_by_portal = False
        self.connector.save()

        response = self.client.put(
            self._integrations_url() + "/" + str(self.connector.id),
            json=integration_update,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        updated_connector = Connector.objects.get(id=self.connector.id)

        # Step 5: Assert that config DID change now
        self.assertEqual(updated_connector.config["access_key"], "updated_access_key")
        self.assertEqual(updated_connector.config["secret_key"], "updated_secret_key")

    def test_delete_integration(self):
        response = self.client.delete(
            self._integrations_url() + "/" + str(self.connector.id)
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        resp = response.json()
        integration = resp

        connector = Connector.objects.get(id=integration["id"])
        self.assertEqual(connector.technology_id, "tenable_io")
        self.assertEqual(connector.is_deleted, True)

        # assert that the connector is not returned in the list of integrations
        response = self.client.get(self._integrations_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 2)

        # assert activity log is created
        last_log = connector.activity_logs.last()
        self.assertEqual(last_log.message, "Integration deleted")

    def test_delete_integration_no_permissions(self):
        self.set_org_permissions(self.organization, set())
        response = self.client.delete(
            self._integrations_url() + "/" + str(self.connector.id)
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_get_integration_health_check(self):
        aad_app = AadAppFactory()
        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="azure_ad",
            version_id="v1",
            config__client_id=aad_app.client_id,
        )

        setup_oauth_responses(connector.config["tenant_id"])

        # Refresh the health check
        url = "/".join(
            [self._integrations_url(), str(connector.id), "refresh_health_checks"]
        )

        # Assert
        response = self.client.post(url)
        self.assertEqual(response.status_code, 200)

        resp = response.json()
        integration = resp
        self.assertEqual(integration["technology_id"], "azure_ad")

        validation = integration["validation"]
        self.assertTrue(validation)

        component = validation[0]
        self.assertEqual(component["id"], "critical-config-valid")
        self.assertIsNone(component["action_type"])

        requirement = component["requirements"][0]
        self.assertEqual(requirement["status"], "passed")

        self.assert_activity_logs(
            connector.id,
            ["Passed health check 'Connection is valid'"],
        )

    def test_get_activity_logs(self):
        aad_app = AadAppFactory()
        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="azure_ad",
            version_id="v1",
            config__client_id=aad_app.client_id,
        )

        def update_ts(batch, ts):
            for item in batch:
                item.timestamp = ts
                item.save()

        logs = ActivityLogFactory.create_batch(
            50,
            connector=connector,
            log_level="warning",
        )
        update_ts(logs, datetime(2020, 10, 1, 0, 0, 0, tzinfo=timezone.utc))

        logs = ActivityLogFactory.create_batch(
            33,
            connector=connector,
            log_level="info",
        )
        update_ts(logs, datetime(2020, 10, 2, 0, 0, 0, tzinfo=timezone.utc))

        logs = ActivityLogFactory.create_batch(
            17,
            connector=connector,
            log_level="error",
            message="Hello",
        )
        update_ts(logs, datetime(2020, 10, 3, 0, 0, 0, tzinfo=timezone.utc))

        setup_oauth_responses(connector.config["tenant_id"])

        resp = self.get(
            connector_id=connector.id,
            path="activity_logs",
        )
        self.assertEqual(len(resp["items"]), 100)

        resp = self.get(
            connector_id=connector.id,
            path="activity_logs",
            params={"timestamp": "2020-10-02T00:00:00.000000Z"},
        )
        self.assertEqual(len(resp["items"]), 50)

        resp = self.get(
            connector_id=connector.id,
            path="activity_logs",
            params={
                "timestamp": "2020-10-02T00:00:00.000000Z&2020-10-02T10:00:00.000000Z"
            },
        )
        self.assertEqual(len(resp["items"]), 33)

        resp = self.get(
            connector_id=connector.id,
            path="activity_logs",
            params={"log_level": "error"},
        )
        self.assertEqual(len(resp["items"]), 17)

        resp = self.get(
            connector_id=connector.id,
            path="activity_logs",
            params={"search": "Hello"},
        )
        self.assertEqual(len(resp["items"]), 17)

        resp = self.get(
            connector_id=connector.id,
            path="activity_logs",
            params={"log_level": ["info", "warning"]},
        )
        self.assertEqual(len(resp["items"]), 83)

    def test_update_vulnerability_mode_force_ignore(self):
        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="qualys_vmpc",
            version_id="v2_0",
            vulnerability_coverage_mode="ignore",
        )
        payload = {"vulnerability_coverage_mode": "n/a"}
        self.put(
            connector.id,
            path="vulnerability_coverage_mode",
            json=payload,
        )
        connector = Connector.objects.get(id=connector.id)
        self.assertEqual(connector.vulnerability_coverage_mode, "enabled")

    def test_null_integration_id(self):
        ConnectorFactory(
            organization=self.organization,
            technology_id="qualys_vmpc",
            version_id="v2_0",
            vulnerability_coverage_mode="enabled",
        )
        payload = {"vulnerability_coverage_mode": "n/a"}
        self.put(
            None, path="vulnerability_coverage_mode", json=payload, status_code=422
        )

    def test_update_vulnerability_mode_force_na(self):
        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="azure_ad",
            version_id="v1",
            vulnerability_coverage_mode="ignore",
        )
        payload = {"vulnerability_coverage_mode": "enabled"}
        self.put(
            connector.id,
            path="vulnerability_coverage_mode",
            json=payload,
        )
        connector = Connector.objects.get(id=connector.id)
        self.assertEqual(connector.vulnerability_coverage_mode, "n/a")

    def test_create_integration_with_vulnerability_coverage_mode(self):
        integration = {
            "technology_id": "tenable_io",
            "version_id": "v1",
            "config": {
                "access_key": "test_access_key",
                "secret_key": "test_secret_key",
            },
            "settings": {
                "host_sync": {"fetch_by_tags": "Category:tag1, Category:tag2"}
            },
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
            "vulnerability_coverage_mode": "enabled",
        }

        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        integration = response.json()
        self.assertEqual(integration["vulnerability_coverage_mode"], "enabled")

    def test_update_endpoint_mode_force_enabled(self):
        """
        Test that updating the endpoint_coverage_mode to 'n/a' forces it to 'enabled' (the default value)
        """
        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="defender_atp",
            version_id="v1",
            endpoint_coverage_mode="ignore",
        )
        payload = {"endpoint_coverage_mode": "n/a"}
        self.put(
            connector.id,
            path="endpoint_coverage_mode",
            json=payload,
        )
        connector = Connector.objects.get(id=connector.id)
        self.assertEqual(connector.endpoint_coverage_mode, "enabled")

    def test_null_integration_id_endpoint_coverage(self):
        ConnectorFactory(
            organization=self.organization,
            technology_id="defender_atp",
            version_id="v1",
            endpoint_coverage_mode="enabled",
        )
        payload = {"endpoint_coverage_mode": "n/a"}
        self.put(None, path="endpoint_coverage_mode", json=payload, status_code=422)

    def test_update_endpoint_coverage_mode_force_na(self):
        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="azure_ad",
            version_id="v1",
            endpoint_coverage_mode="ignore",
        )
        payload = {"endpoint_coverage_mode": "enabled"}
        self.put(
            connector.id,
            path="endpoint_coverage_mode",
            json=payload,
        )
        connector = Connector.objects.get(id=connector.id)
        self.assertEqual(connector.endpoint_coverage_mode, "n/a")

    def test_create_integration_with_endpoint_coverage_mode(self):
        integration = {
            "technology_id": "sentinel_one",
            "version_id": "v2_1",
            "config": {
                "api_key": "test_api_key",
                "url": "https://test_url.com",
            },
            "settings": {"host_sync": {}},
            "organization_id": str(self.organization.id),
            "enabled": True,
            "enabled_actions": ["host_sync"],
            "endpoint_coverage_mode": "enabled",
        }

        response = self.client.post(self._integrations_url(), json=integration)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        integration = response.json()
        self.assertEqual(integration["endpoint_coverage_mode"], "enabled")

    def test_get_activity_logs_paging(self):
        connector = ConnectorFactory(
            organization=self.organization,
            technology_id="tenable_io",
            version_id="v1",
        )
        ActivityLogFactory.create_batch(5, connector=connector, log_level="warning")

        resp = self.get(connector_id=connector.id, path="activity_logs")
        self.assertEqual(len(resp["items"]), 5)

        resp = self.get(
            connector_id=connector.id,
            path="activity_logs",
            params={"limit": 2},
        )
        self.assertEqual(len(resp["items"]), 2)

        resp = self.get(
            connector_id=connector.id,
            path="activity_logs",
            params={"limit": 2, "skip": 2},
        )
        self.assertEqual(len(resp["items"]), 2)

        resp = self.get(
            connector_id=connector.id,
            path="activity_logs",
            params={"limit": 2, "skip": 4},
        )
        self.assertEqual(len(resp["items"]), 1)

    def test_invoke_action_route_disabled_connector(self):
        self.connector.enabled = False
        self.connector.save()
        response = self.invoke_actions(
            self.connector.id,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            path="host_sync",
            json={},
        )
        self.assertEqual(response["error"]["message"], "Connector is disabled")

    def test_invoke_action_route_unhealthy_connector__onboarding_check(self):
        response = self.invoke_actions(
            self.connector.id,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            path="host_sync",
            json={},
        )
        self.assertEqual(response["error"]["message"], "Connector is unhealthy")

    @patch.object(connector_service, "is_action_healthy", return_value=True)
    def test_invoke_action_route_invalid_action(self, is_action_healthy):
        response = self.invoke_actions(
            self.connector.id,
            status_code=status.HTTP_405_METHOD_NOT_ALLOWED,
            path="host_sync",
            json={},
        )
        self.assertEqual(
            response["error"]["message"],
            "Action host_sync is not enabled for this integration",
        )

    @patch.object(connector_service, "is_action_healthy", return_value=True)
    def test_invoke_action_route_valid_action(self, is_action_healthy):
        self.connector.enabled_actions = ["host_sync"]
        self.connector.save()
        self.invoke_actions(
            self.connector.id,
            status_code=status.HTTP_202_ACCEPTED,
            path="host_sync",
            json={},
        )

    def test_cannot_disable_last_enabled_vulnerability_coverage_mode(self):
        # Only one connector with vulnerability_coverage_mode=ENABLED
        connector = self.connector_falcon
        Connector.objects.exclude(id=connector.id).update(
            vulnerability_coverage_mode=CoverageMode.IGNORE
        )
        url = f"{self._integrations_url()}/{connector.id}/vulnerability_coverage_mode"
        data = {"vulnerability_coverage_mode": CoverageMode.IGNORE.value}
        response = self.client.put(url, json=data)
        self.assertEqual(response.status_code, 422)
        self.assertIn(
            "At least one integration must have vulnerability coverage mode enabled.",
            response.text,
        )

    def test_can_disable_enabled_vulnerability_coverage_mode_if_another_enabled_exists(
        self,
    ):
        # Two connectors with vulnerability_coverage_mode=ENABLED
        connector1 = self.connector_falcon
        connector2 = self.connector_falcon_1
        # Disable one, should succeed because the other is still enabled
        url = f"{self._integrations_url()}/{connector1.id}/vulnerability_coverage_mode"
        data = {"vulnerability_coverage_mode": CoverageMode.IGNORE.value}
        response = self.client.put(url, json=data)
        self.assertNotEqual(response.status_code, 422)

        # Check that the first connector's vulnerability coverage mode is now disabled
        connector1.refresh_from_db()
        self.assertEqual(connector1.vulnerability_coverage_mode, CoverageMode.IGNORE)

        # The other should still be enabled
        connector2.refresh_from_db()
        self.assertEqual(connector2.vulnerability_coverage_mode, CoverageMode.ENABLED)

    def test_cannot_disable_last_enabled_endpoint_coverage_mode(self):
        # Only one connector with endpoint_coverage_mode=ENABLED
        self._setup_additional_connectors()
        connector = self.connector_defender_atp
        Connector.objects.exclude(id=connector.id).update(
            endpoint_coverage_mode=CoverageMode.IGNORE
        )
        url = f"{self._integrations_url()}/{connector.id}/endpoint_coverage_mode"
        data = {"endpoint_coverage_mode": CoverageMode.IGNORE.value}
        response = self.client.put(url, json=data)
        self.assertEqual(response.status_code, 422)
        self.assertIn(
            "At least one integration must have endpoint coverage mode enabled.",
            response.text,
        )

    def test_can_disable_enabled_endpoint_coverage_mode_if_another_enabled_exists(
        self,
    ):
        self._setup_additional_connectors()
        connector1 = self.connector_defender_atp
        connector2 = self.connector_defender_atp_1
        # Disable one, should succeed because the other is still enabled
        url = f"{self._integrations_url()}/{connector1.id}/endpoint_coverage_mode"
        data = {"endpoint_coverage_mode": CoverageMode.IGNORE.value}
        response = self.client.put(url, json=data)
        self.assertNotEqual(response.status_code, 422)

        # Check that the first connector's endpoint coverage mode is now disabled
        connector1.refresh_from_db()
        self.assertEqual(connector1.endpoint_coverage_mode, CoverageMode.IGNORE)

        # The other should still be enabled
        connector2.refresh_from_db()
        self.assertEqual(connector2.endpoint_coverage_mode, CoverageMode.ENABLED)

    def assert_activity_logs(self, connector_id, expected: list):
        url = "/".join([self._integrations_url(), str(connector_id), "activity_logs"])
        response = self.client.get(url)
        messages = [item["message"] for item in response.json()["items"]]
        self.assertCountEqual(messages, expected)

    def get(self, connector_id=None, path=None, **kwargs):
        url = self._integrations_url()
        if connector_id:
            url = f"{url}/{connector_id}"
        if path:
            url = f"{url}/{path}"
        response = self.client.get(url, **kwargs)
        self.assertEqual(response.status_code, 200)
        return response.json()

    def put(self, connector_id, status_code=200, path=None, **kwargs):
        url = f"{self._integrations_url()}/{connector_id}"
        if path:
            url = f"{url}/{path}"
        response = self.client.put(url, **kwargs)
        self.assertEqual(response.status_code, status_code)
        return response.json()

    def invoke_actions(self, connector_id, status_code=202, path=None, **kwargs):
        url = f"{self._integrations_url()}/{connector_id}/actions"
        if path:
            url = f"{url}/{path}"
        response = self.client.post(url, **kwargs)
        self.assertEqual(response.status_code, status_code)
        if response.content:
            return response.json()
        return {}

    def _integrations_url(self):
        return "v1/integrations"

    @staticmethod
    def parse_datetime(datetime_str):
        return datetime.strptime(datetime_str, "%Y-%m-%dT%H:%M:%S.%f%z")

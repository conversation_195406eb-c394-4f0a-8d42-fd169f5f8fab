from unittest.mock import ANY, patch

from apps.api.deps import Session
from apps.connectors.integrations import Template
from apps.connectors.integrations.vendors.tenable.tenable_io.v1 import (
    TenableIoV1Config,
    TenableIoV1Settings,
)

from .base import BaseApiTestCase


class TechnologyTests(BaseApiTestCase):
    def test_get_technologies(self):
        response = self.client.get(self._technologies_url())
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(
            len(resp),
            len([t for t in Template.get_all_templates() if not t.is_internal]),
        )
        technology_summary = next(filter(lambda x: x["id"] == "tenable_io", resp))
        self.assertEqual(technology_summary["id"], "tenable_io")
        self.assertEqual(technology_summary["version_ids"], ["v1"])
        self.assertEqual(
            technology_summary["category_name"], "Vulnerability Management"
        )
        self.assertFalse(technology_summary["is_alert_source"])

    @patch.object(Session, "is_available", return_value=True)
    def test_get_technologies_versions(self, _):
        all_technologies = [t.id for t in Template.get_all_templates()]
        for technology_id in all_technologies:
            response = self.client.get(
                self._technologies_url() + f"/{technology_id}/versions"
            )
            self.assertEqual(response.status_code, 200)
            template = Template.get_template(technology_id)
            for item in response.json()["items"]:
                template_version = template.versions[item["id"]]
                self.assertEqual(
                    item["connection_config"],
                    template_version.connection_model.config_model.model_json_schema(),
                )
                self.assertEqual(
                    item["config"],
                    template_version.connection_model.config_model.model_json_schema(),
                )

    def test_get_technology_versions(self):
        response = self.client.get(self._technologies_url() + "/tenable_io/versions")
        self.assertEqual(response.status_code, 200)
        resp = response.json()["items"]
        self.assertEqual(len(resp), 1)
        technology_version = resp[0]
        self.assertEqual(technology_version["id"], "v1")
        self.assertEqual(
            technology_version["supported_actions"],
            [
                "host_sync",
                "vulnerability_asset_sync",
                "detected_vulnerability_sync",
                "vendor_vulnerability_sync",
            ],
        )
        self.assertDictEqual(
            technology_version["config"],
            TenableIoV1Config.model_json_schema(),
        )
        self.assertDictEqual(
            technology_version["settings"]["properties"]["host_sync"],
            TenableIoV1Settings.model_json_schema()["properties"]["host_sync"],
        )
        self.assertEqual(
            technology_version["settings"]["title"],
            "Tenable Vulnerability Management Advanced Settings",
        )
        self.assertEqual(
            technology_version["settings"]["properties"]["host_sync"]["title"],
            "Tenable Vulnerability Management Fetch Hosts Settings",
        )

        self.assertEqual(
            technology_version["actions"]["host_sync"],
            {
                "name": "Fetch hosts",
                "entitlement": "assets:*",
                "settings": {
                    "title": "Tenable Vulnerability Management Fetch Hosts Settings",
                    "type": "object",
                    "properties": ANY,
                },
            },
        )

    def test_get_import_template(self):
        response = self.client.get(
            self._technologies_url() + "/import_hosts/versions/v1/import_template"
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.headers["Content-Disposition"],
            'attachment; filename="import_asset_template.xlsx"',
        )
        self.assertEqual(
            response.headers["Content-Type"],
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )

    def test_get_hidden_technology_versions(self):
        response = self.client.get(self._technologies_url() + "/vulncheck/versions")
        self.assertEqual(response.status_code, 404)

    def test_get_hidden_technology_version(self):
        response = self.client.get(self._technologies_url() + "/vulncheck/versions/v1")
        self.assertEqual(response.status_code, 404)

    def test_get_technology_version(self):
        response = self.client.get(self._technologies_url() + "/tenable_io/versions/v1")
        self.assertEqual(response.status_code, 200)
        resp = response.json()
        technology_version = resp
        self.assertEqual(technology_version["id"], "v1")
        self.assertEqual(
            technology_version["supported_actions"],
            [
                "host_sync",
                "vulnerability_asset_sync",
                "detected_vulnerability_sync",
                "vendor_vulnerability_sync",
            ],
        )
        self.assertDictEqual(
            technology_version["config"],
            TenableIoV1Config.model_json_schema(),
        )

    def _technologies_url(self):
        return "v1/technologies"

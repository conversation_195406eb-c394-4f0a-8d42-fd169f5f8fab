from unittest.mock import Mock, patch

from criticalstart.auth.v2.test.factory import Organization, UserSession
from requests.exceptions import HTTPError

from apps.api.deps import AccountsApi, Session, convert_user_session
from apps.tests.base import BaseTestCase


class SessionTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.session = convert_user_session(UserSession(organization=Organization()))

    @patch.object(AccountsApi, "get_entitlement")
    def test_get_entitlement_api_exception(self, mget_entitlement):
        mget_entitlement.side_effect = HTTPError(response=Mock(status_code=500))
        with self.assertRaises(expected_exception=HTTPError):
            self.session.get_entitlement("test")

    @patch.object(Session, "get_entitlement")
    def test_is_available_internal(self, mget_entitlement):
        self.assertFalse(self.session.is_available(Mock(is_internal=True)))
        mget_entitlement.assert_not_called()

    @patch.object(AccountsApi, "get_entitlement")
    def test_http_error(self, mget_entitlement):
        mget_entitlement.side_effect = HTTPError(response=Mock(status_code=404))
        self.assertEqual({}, self.session.get_entitlement("test"))
        mget_entitlement.assert_called_once()

    def test_no_account(self):
        self.session.organization.account_id = None
        entitlement = self.session.get_entitlement("test")
        self.assertEqual(entitlement, {})

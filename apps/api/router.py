from fastapi import APIRouter, Depends

from apps.api.deps import session
from apps.api.v1.routers import (
    aad_app,
    connection,
    integration,
    technology,
    vendor,
    version,
)

org_based_routers = [
    aad_app.router,
    technology.router,
    integration.router,
    connection.router,
    vendor.router,
]

router = APIRouter()
for r in org_based_routers:
    router.include_router(
        r,
        prefix="/v1",
        dependencies=[Depends(session)],
    )

router.include_router(version.router, prefix="/v1")

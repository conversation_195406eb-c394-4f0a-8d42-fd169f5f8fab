from typing import Annotated, Union

from criticalstart.auth.v2.integrations.fastapi import UserSession, UserSessionToken
from django.conf import settings
from fastapi import Depends
from fastapi import status as http_status
from requests.exceptions import HTTPError

from apps.accounts.api.accounts import AccountsApi
from apps.connectors.integrations.template import BaseTemplate
from apps.connectors.models import Connector

session = UserSessionToken(
    base_url=settings.ATA_AUTH_MICROSERVICE_URL,
    secret_key=settings.ATA_AUTH_MICROSERVICE_SECRET_KEY,
)


class Session(UserSession):
    """
    Helper class that allows to extend the UserSession with additional properties,
    and maintain request context during the request.
    """

    _entitlements_cache: dict[str, dict] = {}

    def get_entitlement(self, module: str) -> dict:
        account_id = self.organization.account_id

        if account_id is None:
            return {}

        cache_key = f"{account_id}_{module}"
        if cache_key not in self._entitlements_cache:
            try:
                accounts_api = AccountsApi(self.token)
                self._entitlements_cache[cache_key] = accounts_api.get_entitlement(
                    account_id, module
                )
            except HTTPError as e:
                if e.response.status_code == http_status.HTTP_404_NOT_FOUND:
                    self._entitlements_cache[cache_key] = {}
                else:
                    raise
        return self._entitlements_cache[cache_key]

    def is_available(self, entity: Union[Connector, BaseTemplate]) -> bool:
        if entity.is_internal:
            return False
        if isinstance(entity, Connector):
            entity = entity.template
        return True


def convert_user_session(user_session: UserSession = Depends(session)):
    return Session(**user_session.model_dump())


SessionDep = Annotated[Session, Depends(convert_user_session)]

import uuid

from django.db import models


class Account(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    alias = models.Char<PERSON>ield(
        max_length=255, verbose_name="Account alias", null=True, blank=True
    )

    def __repr__(self):
        return f"Account(" f"id={self.id!r}, " f"alias={self.alias!r})"

    def __str__(self):
        return f"<Account: {self.alias}>"


class Organization(models.Model):
    class SyncStatus(models.TextChoices):
        SYNCED = "synced"
        ORPHANED = "orphaned"
        LOCAL = "local"

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    alias = models.Char<PERSON>ield(max_length=255, verbose_name="Organization alias")
    account = models.ForeignKey(Account, on_delete=models.RESTRICT)
    sync_status = models.Char<PERSON><PERSON>(
        max_length=50, choices=SyncStatus.choices, default=SyncStatus.SYNCED
    )

    class Meta:
        indexes = [
            models.Index(fields=["sync_status"]),
        ]

    def __repr__(self):
        return (
            f"Organization("
            f"id={self.id!r}, "
            f"alias={self.alias!r}, "
            f"account={self.account!r}"
            f")"
        )

    def __str__(self):
        return f"<Organization: {self.alias}>"

from django.contrib import admin
from django_admin_listfilter_dropdown.filters import DropdownFilter

from apps.accounts.models import Account, Organization


@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    list_display = ("id", "alias")
    search_fields = ("alias",)


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ("id", "alias", "account", "sync_status")
    list_filter = [("account__alias", DropdownFilter), "sync_status"]
    search_fields = ("id", "alias")

import responses
from django.conf import settings
from django.test import TestCase

from apps.accounts.api.accounts import AccountsApi
from factories import OrganizationFactory


class AccountsApiTestCase(TestCase):
    @responses.activate
    def test_get_entitlement(self):
        org = OrganizationFactory()
        url = (
            f"{settings.ACCOUNTS_MICROSERVICE_URL}/api/v1/accounts/"
            f"{org.account.id}/entitlements/module/mdr:core/active"
        )
        responses.add(responses.GET, url=url, json={"is_active": True})

        api = AccountsApi("token")
        response = api.get_entitlement(org.account.id, "mdr:core")
        self.assertEqual(response, {"is_active": True})

from django.contrib.auth import get_user_model
from django.test import TestCase

User = get_user_model()


class SessionAdminTestCase(TestCase):
    def setUp(self):
        super().setUp()
        self.user = User.objects.create_user(
            username="admin-user", password="password", is_staff=True, is_superuser=True
        )
        self.client.login(username="admin-user", password="password")

    def test_sessions_list_view(self):
        """Ensure list of sessions is accessible"""
        response = self.client.get("/sessions/session/")
        self.assertEqual(200, response.status_code)

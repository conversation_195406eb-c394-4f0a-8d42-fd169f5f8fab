from django.test import TestCase

from apps.accounts.models import Account, Organization


class AccountTests(TestCase):
    def test_account(self):
        account = Account.objects.create(alias="fake-account")
        self.assertEqual(
            repr(account), "Account(id=%r, alias=%r)" % (account.id, account.alias)
        )
        self.assertEqual(str(account), "<Account: fake-account>")

        org = Organization.objects.create(
            id="559a4855-b353-48b7-8ae7-19bc2e12e929",
            alias="fake-organization",
            account=account,
        )
        self.assertEqual(
            repr(org),
            "Organization(id=%r, alias=%r, account=%r)"
            % (org.id, org.alias, org.account),
        )
        self.assertEqual(str(org), "<Organization: fake-organization>")

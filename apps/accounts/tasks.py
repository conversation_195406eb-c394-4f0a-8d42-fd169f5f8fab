from celery import shared_task
from criticalstart.auth.v2.client import AuthInternalClient
from criticalstart.auth.v2.integrations.httpx import AnonymousService
from django.conf import settings

from apps.accounts.models import Account, Organization

client = AuthInternalClient(
    base_url=settings.ATA_AUTH_MICROSERVICE_URL,
    auth=AnonymousService(settings.ATA_AUTH_MICROSERVICE_SECRET_KEY),
)


@shared_task()
def sync_organizations():
    """
    Sync organizations from the auth service.
    """
    org_list = client.organizations.list()

    for org in org_list.items:
        if org.account_id:
            account, _ = Account.objects.update_or_create(
                id=org.account_id,
                defaults={
                    "alias": org.account_alias,
                },
            )
        else:
            account, _ = Account.objects.get_or_create(alias="None")

        Organization.objects.update_or_create(
            id=org.id,
            defaults={
                "alias": org.alias,
                "account": account,
                "sync_status": Organization.SyncStatus.SYNCED,
            },
        )

    # Set orphan status for organizations that are not in the list
    api_ids = {org.id for org in org_list.items}
    synced_ids = set(
        Organization.objects.filter(
            sync_status=Organization.SyncStatus.SYNCED
        ).values_list("id", flat=True)
    )
    orphaned_ids = synced_ids - api_ids
    Organization.objects.filter(id__in=orphaned_ids).update(
        sync_status=Organization.SyncStatus.ORPHANED
    )

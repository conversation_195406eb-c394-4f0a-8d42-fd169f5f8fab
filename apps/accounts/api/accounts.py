import logging
from typing import Union
from uuid import UUID

from ata_common.http import TimeoutSession
from criticalstart.auth.v2.integrations.requests import HTTPBearer
from requests.compat import urljoin

from core import settings

logger = logging.getLogger(__name__)

TIMEOUT = 60  # seconds


class AccountsApi:
    api_url = urljoin(settings.ACCOUNTS_MICROSERVICE_URL, "api/v1/")

    def __init__(self, auth_token: str):
        self.__session = TimeoutSession(TIMEOUT)
        self.__session.auth = HTTPBearer(auth_token)

    def _get(self, url: str, params: Union[dict, None] = None) -> dict:
        response = self.__session.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def get_entitlement(
        self, account_id: Union[str | UUID], entitlement_module: str
    ) -> dict:
        url = urljoin(
            self.api_url,
            f"accounts/{account_id}/entitlements/module/{entitlement_module}/active",
        )
        return self._get(url)

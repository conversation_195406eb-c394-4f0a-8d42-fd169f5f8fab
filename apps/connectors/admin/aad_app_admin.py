from django.contrib import admin
from django_admin_listfilter_dropdown.filters import DropdownFilter

from apps.connectors.models import AadApp


@admin.register(AadApp)
class AadAppAdmin(admin.ModelAdmin):
    list_display = ("name", "client_id", "organization_alias")
    list_filter = [
        ("organization__alias", DropdownFilter),
    ]
    readonly_fields = ("created_at", "updated_at")

    def organization_alias(self, obj):  # pragma: no cover
        return obj.organization.alias if obj.organization else None

    organization_alias.short_description = "Organization Alias"

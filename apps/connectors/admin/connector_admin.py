import json

from django.contrib import admin
from django.forms import widgets
from django.shortcuts import redirect
from django.urls import path
from django.utils.safestring import mark_safe
from django_admin_listfilter_dropdown.filters import DropdownFilter
from pygments import highlight
from pygments.formatters.html import Htm<PERSON><PERSON><PERSON>atter
from pygments.lexers.data import <PERSON><PERSON><PERSON><PERSON><PERSON>

from apps.connectors.integrations.template import Template
from apps.connectors.models import Connector
from apps.connectors.services import connector_service


class HealthCheckAdminFilter(admin.SimpleListFilter):  # pragma: no cover
    title = "Health Check Status"
    parameter_name = "health_check"

    def lookups(self, request, model_admin):
        """
        Define the filter options.
        The tuples are (value in URL, human-readable name)
        """
        return (
            ("healthy", "Healthy"),
            ("unhealthy", "Unhealthy"),
            ("unknown", "Unknown"),
        )

    def queryset(self, request, queryset):
        """
        Filter the queryset based on the health_check_status property in the admin.
        Since this property is not on the model, we need to loop through the objects.
        """
        # Short-circuit if the filter is not active
        if not self.value():
            return queryset

        model_admin = ConnectorAdmin(Connector, admin.site)

        healthy_ids = []
        unhealthy_ids = []
        unknown_ids = []

        for obj in queryset:
            health_status = model_admin.health_check_status(obj)
            if health_status is True:
                healthy_ids.append(obj.pk)
            elif health_status is False:
                unhealthy_ids.append(obj.pk)
            else:
                unknown_ids.append(obj.pk)

        if self.value() == "healthy":
            return queryset.filter(pk__in=healthy_ids)
        elif self.value() == "unhealthy":
            return queryset.filter(pk__in=unhealthy_ids)
        elif self.value() == "unknown":
            return queryset.filter(pk__in=unknown_ids)
        return queryset


class ReadOnlyJSONData:
    @staticmethod
    def render(value):
        formatter = HtmlFormatter(style="material")
        style = "<style>" + formatter.get_style_defs() + "</style><br>"
        highlighted = highlight(json.dumps(value, indent=4), JsonLexer(), formatter)
        return mark_safe(f'{style} <div class="hll"> {highlighted} </div>')


@admin.register(Connector)
class ConnectorAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "organization_alias",
        "technology_id",
        "enabled_actions",
        "is_deleted",
        "health_check_status",
        "get_artifact_path",
    )
    list_filter = [
        ("technology_id", DropdownFilter),
        ("connection__connection_template_id", DropdownFilter),
        ("organization__account__alias", DropdownFilter),
        ("organization__alias", DropdownFilter),
        "is_deleted",
        HealthCheckAdminFilter,
    ]
    search_fields = (
        "id",
        "technology_id",
        "enabled_actions",
        "organization__alias",
        "organization__id",
        "organization__account__alias",
        "organization__account__id",
        "connection__id",
        "connection__connection_template_id",
    )
    readonly_fields = (
        "created_at",
        "updated_at",
        "last_activity_at",
        "health_check_status",
        "health_check_results",
    )

    change_form_template = "connector_change_form.html"

    def has_delete_permission(self, request, obj=None):
        return False

    def get_form(self, request, obj, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        templates = Template.get_all_templates()
        form.base_fields["technology_id"].widget = widgets.Select(
            choices=[(template.id, template.id) for template in templates]
        )

        return form

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                "run_health_check/<str:object_id>/",
                self.admin_site.admin_view(self.run_health_check),
                name="run_health_check",
            ),
        ]
        return custom_urls + urls

    @admin.display(description="Organization Alias", ordering="organization__alias")
    def organization_alias(self, obj):  # pragma: no cover
        return obj.organization.alias

    @admin.display(description="Artifact Path")
    def get_artifact_path(self, obj):  # pragma: no cover
        from apps.connectors.services.artifact_service import artifact_service

        art_key = artifact_service.build_base_key(
            obj.organization.id, obj.technology_id, obj.version_id, obj.id
        )
        url = artifact_service.get_url(art_key)
        return mark_safe(f"<a href={url}>Artifact Path</a>")

    @admin.display(boolean=True)
    def health_check_status(self, obj):
        return connector_service.is_healthy(obj, force_refresh=False)

    def health_check_results(self, obj):
        result = connector_service.get_health_check(obj)
        if result is None:
            return "No health check results are cached. Please run a health check to see results."
        if not result:
            return "No health checks are configured for this connector template."
        dict_result = [r.to_dict() for r in result]
        colorful_json = ReadOnlyJSONData.render(dict_result)
        return colorful_json

    def run_health_check(self, request, object_id, *args, **kwargs):  # pragma: no cover
        obj = self.get_object(request, object_id)
        connector_service.refresh_health_check(obj)
        return redirect(request.META.get("HTTP_REFERER"))

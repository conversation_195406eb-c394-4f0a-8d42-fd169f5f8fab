from django.contrib import admin
from django_admin_listfilter_dropdown.filters import DropdownFilter

from apps.connectors.models import ActivityLog


@admin.register(ActivityLog)
class ActivityLogAdmin(admin.ModelAdmin):
    list_display = ["id", "connector_id", "timestamp", "message", "log_level"]
    list_filter = [
        ("connector__id", DropdownFilter),
        "log_level",
    ]
    search_fields = ["message"]

from django.contrib import admin
from django_admin_listfilter_dropdown.filters import DropdownFilter

from apps.connectors.models import Connection


@admin.register(Connection)
class ConnectionAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "organization_alias",
        "connection_template_id",
        "name",
        "is_deleted",
    )
    list_filter = [
        ("connection_template_id", DropdownFilter),
        ("organization__account__alias", DropdownFilter),
        ("organization__alias", DropdownFilter),
        "is_deleted",
    ]
    search_fields = (
        "id",
        "organization__alias",
        "organization__id",
        "organization__account__alias",
        "organization__account__id",
    )

    @admin.display(description="Organization Alias", ordering="organization__alias")
    def organization_alias(self, obj):  # pragma: no cover
        return obj.organization.alias

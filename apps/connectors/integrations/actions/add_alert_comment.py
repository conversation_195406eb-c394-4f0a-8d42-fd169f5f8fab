from pydantic import BaseModel

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs


class AddAlertCommentResult(BaseModel):
    ...


class AddAlertCommentArgs(IntegrationActionArgs):
    vendor_sync_id: str
    comment: str


class AddAlertComment(IntegrationAction):
    name = "Add Alert Comment"
    action_type = IntegrationActionType.ADD_ALERT_COMMENT
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=AddAlertCommentArgs,
        result_type=AddAlertCommentResult,
    )

from pydantic import Field

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    IntegrationIdentifierArgs,
    MailboxRuleIdentifier,
    MailMessageIdentifier,
    Message,
    TAPResult,
    UPNIdentifier,
    UserIdentifier,
)


class EmailActionArgs(IntegrationIdentifierArgs):
    user_id: UserIdentifier = Field(description="User ID")
    message_id: MailMessageIdentifier = Field(
        description="Message ID",
    )


class EmailActionByUPNArgs(IntegrationIdentifierArgs):
    upn: UPNIdentifier = Field(description="User Principal Name")
    message_id: MailMessageIdentifier = Field(
        description="Message ID",
    )


class DeleteMailboxRuleArgs(IntegrationIdentifierArgs):
    user_id: UserIdentifier = Field(description="User ID")
    rule_id: MailboxRuleIdentifier = Field(description="Rule ID")


class DeleteMailboxRuleByUPNArgs(IntegrationIdentifierArgs):
    upn: UPNIdentifier = Field(description="User Principal Name")
    rule_id: MailboxRuleIdentifier = Field(description="Rule ID")


class EmailActionResult(TAPResult[Message]):
    ...


class DeleteMailboxRuleResult(TAPResult[Message]):
    ...


email_action_metadata = IntegrationActionMetadata(
    args_type=EmailActionArgs,
    result_type=EmailActionResult,
)

email_action_by_upn_metadata = IntegrationActionMetadata(
    args_type=EmailActionByUPNArgs,
    result_type=EmailActionResult,
)

delete_mailbox_rule_by_upn_metadata = IntegrationActionMetadata(
    args_type=DeleteMailboxRuleByUPNArgs,
    result_type=DeleteMailboxRuleResult,
)


class DeleteEmailAction(IntegrationAction):
    name = "Delete Email"
    action_type = IntegrationActionType.DELETE_EMAIL
    entitlement = Entitlement.mdr
    metadata = email_action_metadata


class DeleteEmailByUPNAction(IntegrationAction):
    name = "Delete Email by UPN"
    action_type = IntegrationActionType.DELETE_EMAIL_BY_UPN
    entitlement = Entitlement.mdr
    metadata = email_action_by_upn_metadata


class RestoreEmailAction(IntegrationAction):
    name = "Restore Email"
    action_type = IntegrationActionType.RESTORE_EMAIL
    entitlement = Entitlement.mdr
    metadata = email_action_metadata


class RestoreEmailByUPNAction(IntegrationAction):
    name = "Restore Email by UPN"
    action_type = IntegrationActionType.RESTORE_EMAIL_BY_UPN
    entitlement = Entitlement.mdr
    metadata = email_action_by_upn_metadata


class DeleteMailboxRuleAction(IntegrationAction):
    name = "Delete Mailbox Rule"
    action_type = IntegrationActionType.DELETE_MAILBOX_RULE
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=DeleteMailboxRuleArgs,
        result_type=DeleteMailboxRuleResult,
    )


class DeleteMailboxRuleByUPNAction(IntegrationAction):
    name = "Delete Mailbox Rule by UPN"
    action_type = IntegrationActionType.DELETE_MAILBOX_RULE_BY_UPN
    entitlement = Entitlement.mdr
    metadata = delete_mailbox_rule_by_upn_metadata

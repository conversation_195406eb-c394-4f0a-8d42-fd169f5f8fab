from datetime import datetime

from criticalstart.fastapi_utils import OptionalParam
from pydantic import BaseModel, Field

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs


class VulnerabilityIntelligenceSyncArgs(IntegrationActionArgs):
    since: OptionalParam[datetime] = Field(
        description="Timestamp to sync data since. If not provided, sync all data.",
        default=None,
    )


class CVE(BaseModel):
    source_id: str = Field(description="CVE id.")
    description: OptionalParam[str]

    # cvss
    cvss_v3_base_score: OptionalParam[float]
    cvss_v2_base_score: OptionalParam[float]

    # epss
    epss_score: OptionalParam[float]
    epss_percentile: OptionalParam[float]
    epss_last_modified: OptionalParam[datetime]

    # exploit types (threats)
    exploited_initial_access: bool = Field(default=False)
    exploited_remote_with_credentials: bool = Field(default=False)
    exploited_local: bool = Field(default=False)
    exploited_client_side: bool = Field(default=False)
    exploited_info_leak: bool = Field(default=False)
    exploited_denial_of_service: bool = Field(default=False)

    # For now, we are using the naming as advised by the Threat Intelligence Vendor
    # (VulnCheck) but we can change it to a more generic naming if needed.  It is
    # still a bit unclear if "found" vs "reported" has any meaningful distinction or
    # it is inconsistent naming on their side.
    commercial_exploit_found: bool = Field(default=False)
    weaponized_exploit_found: bool = Field(default=False)

    reported_exploited: bool = Field(default=False)
    reported_exploited_by_threat_actors: bool = Field(default=False)
    reported_exploited_by_ransomware: bool = Field(default=False)
    reported_exploited_by_botnets: bool = Field(default=False)

    in_cisa_kev: bool = Field(default=False)
    in_vulncheck_kev: bool = Field(default=False)


class VulnerabilityIntelligenceSync(IntegrationAction):
    name = "Fetch vulnerability intelligence"
    action_type = IntegrationActionType.VULNERABILITY_INTELLIGENCE_SYNC
    entitlement = Entitlement.internal_system
    metadata = IntegrationActionMetadata(
        args_type=VulnerabilityIntelligenceSyncArgs,
        result_type=CVE,
    )

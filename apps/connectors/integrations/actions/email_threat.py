from enum import StrEnum
from typing import List

from pydantic import BaseModel, Field

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    EmailThreatIdentifierArgs,
    IntegrationActionPollingContext,
    TAPResult,
    ocsf,
)


class MailMessage(BaseModel):
    source_id: str = Field(
        description="The ID of the email message in the source technology.",
    )
    technology_id: str = Field(
        description="Source technology ID.",
        default="",
    )
    internet_message_id: str = Field(
        description="Internet Message ID of the email message."
    )
    sender_address: str = Field(
        description="The email address of the sender.",
        default="",
    )
    recipient_addresses: List[str] = Field(
        description="The email addresses of the recipient.",
        default=[],
    )
    subject: str = Field(
        description="The subject of the email.",
        default="",
    )
    source_remediation_status: str = Field(
        description="Remediation status reported by source technology",
        default="",
    )


class ThreatLink(BaseModel):
    source_id: str = Field(
        description="The ID of the email message in the source technology.",
    )
    url: ocsf.Url = Field(
        description="The URLs in the email message.",
        default=[],
    )


class EmailThreatRemediationStatus(StrEnum):
    NONE = "none"
    IN_PROGRESS = "in_progress"
    REMEDIATED = "remediated"
    UNREMEDIATED = "unremediated"


class EmailThreatInfo(BaseModel):
    technology_id: str = Field(description="Source technology ID.", default="")
    source_id: str = Field(
        description="The ID of the email threat in the source technology to perform the action on.",
        default="",
    )
    messages: List[MailMessage] = Field(
        description="The email messages the threat is related to."
    )
    remediation_status: EmailThreatRemediationStatus = Field(
        description="The remediation status of the threat.",
        default=EmailThreatRemediationStatus.NONE,
    )


class EmailThreatResult(TAPResult[EmailThreatInfo]):
    ...


class EmailThreatRemediationResult(TAPResult[EmailThreatInfo]):
    ...


class GetAttachmentDetailsByEmailThreatResult(TAPResult[list[ocsf.File]]):
    ...


class GetUrlsByEmailThreatResult(TAPResult[list[ThreatLink]]):
    ...


class GetEmailThreat(IntegrationAction):
    name = "Get email threat"
    action_type = IntegrationActionType.GET_EMAIL_THREAT
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=EmailThreatIdentifierArgs,
        result_type=EmailThreatResult,
    )


class RemediateEmailThreat(IntegrationAction):
    name = "Remediate email threat"
    action_type = IntegrationActionType.REMEDIATE_EMAIL_THREAT
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=EmailThreatIdentifierArgs,
        result_type=EmailThreatRemediationResult | IntegrationActionPollingContext,
    )


class UnRemediateEmailThreat(IntegrationAction):
    name = "Unremediate email threat"
    action_type = IntegrationActionType.UNREMEDIATE_EMAIL_THREAT
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=EmailThreatIdentifierArgs,
        result_type=EmailThreatRemediationResult | IntegrationActionPollingContext,
    )


class GetAttachmentDetailsByEmailThreat(IntegrationAction):
    name = "Get attachment details by email threat"
    action_type = IntegrationActionType.GET_ATTACHMENT_DETAILS_BY_EMAIL_THREAT
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=EmailThreatIdentifierArgs,
        result_type=GetAttachmentDetailsByEmailThreatResult,
    )


class GetUrlsByEmailThreat(IntegrationAction):
    name = "Get URLs by email threat"
    action_type = IntegrationActionType.GET_URLS_BY_EMAIL_THREAT
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=EmailThreatIdentifierArgs,
        result_type=GetUrlsByEmailThreatResult,
    )

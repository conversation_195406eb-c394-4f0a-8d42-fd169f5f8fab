from __future__ import annotations

from abc import ABC, abstractmethod
from collections.abc import Generator
from dataclasses import dataclass
from enum import StrEnum
from types import UnionType
from typing import (
    TYPE_CHECKING,
    Annotated,
    Any,
    List,
    Optional,
    Sequence,
    Union,
    get_args,
    get_origin,
    get_type_hints,
)

from pydantic import BaseModel, field_validator

if TYPE_CHECKING:
    from apps.connectors.integrations.health_check import (
        IntegrationPermissionsHealthCheck,
    )
from apps.connectors.integrations.schemas import (
    Identifier,
    IntegrationActionPollingContext,
)
from apps.connectors.integrations.schemas.action_args import (
    IntegrationActionArgs,
    IntegrationIdentifierArgs,
)


# Fetch-related actions (the "_sync" ones)
class FetchIntegrationActionType(StrEnum):
    HOST_SYNC = "host_sync"
    VULNERABILITY_ASSET_SYNC = "vulnerability_asset_sync"
    DETECTED_VULNERABILITY_SYNC = "detected_vulnerability_sync"
    VENDOR_VULNERABILITY_SYNC = "vendor_vulnerability_sync"
    VULNERABILITY_INTELLIGENCE_SYNC = "vulnerability_intelligence_sync"
    EVENT_SYNC = "event_sync"
    EVENT_SYNC_FROM_ARTIFACT = "event_sync_from_artifact"


# Host account managment actions
class HostIntegrationActionType(StrEnum):
    GET_HOST_INFO = "get_host_info"
    ADD_HOST_TO_WATCHLIST = "add_host_to_watchlist"
    REMOVE_HOST_FROM_WATCHLIST = "remove_host_from_watchlist"
    ISOLATE_HOST = "isolate_host"
    UNISOLATE_HOST = "unisolate_host"


# User account management actions
class UserIntegrationActionType(StrEnum):
    CONFIRM_RISKY_USER = "confirm_risky_user"
    CONFIRM_RISKY_USER_BY_UPN = "confirm_risky_user_by_upn"
    DISABLE_USER_LOGIN = "disable_user_login"
    DISABLE_USER_LOGIN_BY_UPN = "disable_user_login_by_upn"
    DISMISS_RISKY_USER = "dismiss_risky_user"
    DISMISS_RISKY_USER_BY_UPN = "dismiss_risky_user_by_upn"
    ENABLE_USER_LOGIN = "enable_user_login"
    ENABLE_USER_LOGIN_BY_UPN = "enable_user_login_by_upn"
    LOCK_USER = "lock_user"
    UNLOCK_USER = "unlock_user"
    GET_USER_INFO = "get_user_info"
    GET_USER_INFO_BY_UPN = "get_user_info_by_upn"
    RESET_USER_PASSWORD = "reset_user_password"
    RESET_USER_PASSWORD_BY_UPN = "reset_user_password_by_upn"
    REVOKE_USER_SESSIONS = "revoke_user_sessions"
    REVOKE_USER_SESSIONS_BY_UPN = "revoke_user_sessions_by_upn"
    GET_SIGN_IN_LOGS_BY_USER_ID = "get_sign_in_logs_by_user"
    GET_SIGN_IN_LOGS_BY_UPN = "get_sign_in_logs_by_UPN"
    GET_SIGN_IN_LOGS_BY_IP = "get_sign_in_logs_by_ip"
    GET_EXTERNAL_USER_PROFILE_LINK = "get_external_user_profile_link"
    ADD_USER_TO_WATCHLIST = "add_user_to_watchlist"
    REMOVE_USER_FROM_WATCHLIST = "remove_user_from_watchlist"


# Email threat management actions
class EmailIntegrationActionType(StrEnum):
    GET_EMAIL_THREAT = "get_email_threat"
    REMEDIATE_EMAIL_THREAT = "remediate_email_threat"
    UNREMEDIATE_EMAIL_THREAT = "unremediate_email_threat"
    DELETE_EMAIL = "delete_email"
    DELETE_EMAIL_BY_UPN = "delete_email_by_upn"
    RESTORE_EMAIL = "restore_email"
    RESTORE_EMAIL_BY_UPN = "restore_email_by_upn"
    DELETE_MAILBOX_RULE = "delete_mailbox_rule"
    DELETE_MAILBOX_RULE_BY_UPN = "delete_mailbox_rule_by_upn"
    GET_ATTACHMENT_DETAILS_BY_EMAIL_THREAT = "get_attachment_details_by_email_threat"
    GET_URLS_BY_EMAIL_THREAT = "get_urls_by_email_threat"
    GET_URL_CLICKS_BY_URL = "get_url_clicks_by_url"
    GET_URL_CLICKS_BY_MESSAGE = "get_url_clicks_by_message"
    GET_URL_CLICKS_BY_MESSAGE_SENDER = "get_url_clicks_by_message_sender"
    GET_SIMILAR_EMAILS = "get_similar_emails"
    GET_EMAIL_ACTIVITY_BY_SENDER = "get_email_activity_by_sender"


# IOC management actions
class IOCIntegrationActionType(StrEnum):
    DELETE_IOC = "delete_ioc"
    LIST_IOCS = "list_iocs"
    SAVE_IOC = "save_ioc"
    LIST_DATA_SOURCES = "list_data_sources"


# Alert actions
class AlertIntegrationActionType(StrEnum):
    UPDATE_LIFECYCLE_STATUS = "update_lifecycle_status"
    ADD_ALERT_COMMENT = "add_alert_comment"


# Utility or miscellaneous actions
class UtilityIntegrationActionType(StrEnum):
    AI_COMPLETION = "ai_completion"
    CHECK_IP_REPUTATION = "check_ip_reputation"
    DECODE_URL = "decode_url"


# Firewall actions
class FirewallIntegrationActionType(StrEnum):
    BLOCK_IP_FIREWALL = "block_ip_firewall"
    UNBLOCK_IP_FIREWALL = "unblock_ip_firewall"
    QUARANTINE_IP_FIREWALL = "quarantine_ip_firewall"
    UNQUARANTINE_IP_FIREWALL = "unquarantine_ip_firewall"


IntegrationActionType = StrEnum(
    "IntegrationActionType",
    {
        **FetchIntegrationActionType.__members__,
        **UserIntegrationActionType.__members__,
        **EmailIntegrationActionType.__members__,
        **IOCIntegrationActionType.__members__,
        **AlertIntegrationActionType.__members__,
        **UtilityIntegrationActionType.__members__,
        **FirewallIntegrationActionType.__members__,
        **HostIntegrationActionType.__members__,
    },
)


class InvocationType(StrEnum):
    # Internal means that the integration is only accessible to internal users (eg:
    # the platform itself)
    INTERNAL = "Internal"
    # Public means that the integration is accessible to an actual CORR user
    PUBLIC = "Public"


@dataclass
class TechnologyIntegrationActionType:
    technology_id: str
    action_type: IntegrationActionType


class IntegrationActionMetadata(BaseModel):
    args_type: type[IntegrationActionArgs]
    result_type: Any
    is_generator: bool = False
    supports_polling: bool = False

    @field_validator("result_type")
    def validate_result_type_field_value(cls, value):  # noqa: N805
        origin = get_origin(value)
        if origin is Union or origin is UnionType:
            for typ in get_args(value):
                if not issubclass(typ, BaseModel):
                    raise ValueError(
                        f"result_type {typ} must be a subclass of BaseModel"
                    )
        elif not issubclass(value, BaseModel):
            raise ValueError(f"result_type {value} must be a subclass of BaseModel")

        return value

    def validate_args_type(self, args_type):
        def _validate_identifier_args(args_type, annotation):
            """
            Check that if the annotation is an Identifier, the args_type is a subclass of IntegrationIdentifierArgs
            """
            origin = get_origin(annotation)
            args = get_args(annotation)

            if origin is Annotated:
                return

            if origin is Union or origin in (list, List):
                for arg in args:
                    _validate_identifier_args(args_type, arg)
            else:
                if issubclass(annotation, Identifier) and not issubclass(
                    args_type, IntegrationIdentifierArgs
                ):
                    raise ValueError(
                        f"Action args type {args_type} must be a subclass of IntegrationIdentifierArgs"
                    )  # pragma: no cover

        if not issubclass(args_type, IntegrationActionArgs):
            raise ValueError(
                f"Action args type {args_type} must be a subclass of IntegrationActionArgs"
            )

        # Check that if at least one field is an Identifier, the args_type is a subclass of IntegrationIdentifierArgs
        for _, field in args_type.model_fields.items():
            _validate_identifier_args(args_type, field.annotation)

        if args_type == self.args_type:
            return

        raise ValueError(
            f"Action args type {args_type} must match {self.args_type}"
        )  # pragma: no cover

    def validate_result_type(self, result_type):
        if result_type == self.result_type:
            return

        if get_origin(result_type) == Generator:
            if get_args(result_type)[0] == self.result_type:
                self.is_generator = True
                return

        ret = False
        origin = get_origin(self.result_type)
        if origin is Union or origin is UnionType:
            for typ in get_args(self.result_type):
                if typ == result_type:
                    ret = True
                if typ == IntegrationActionPollingContext:
                    self.supports_polling = True

        if ret:
            return ret

        raise ValueError(
            f"Action return type {result_type} must match {self.result_type}"
        )


class IntegrationAction(ABC):
    _all_types = {}

    name: str = None
    action_type: Union[IntegrationActionType, str] = None
    entitlement: str = None
    metadata: IntegrationActionMetadata = None
    invocation_type: str = InvocationType.INTERNAL
    namespace: Optional[str] = None  # None implies that this is a global action

    def __init__(self, integration, settings):
        self.integration = integration
        self.settings = settings

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)

        if cls.action_type is None:
            raise ValueError(
                "action_type must be defined on IntegrationAction subclasses"
            )

        if "action_type" in cls.__dict__:
            if "/" in cls.action_type:
                cls.namespace = cls.action_type.split("/")[0]

                # unlike base action classes, namespaced actions are expected
                # to be implemented and as a result should be validated
                cls.validate_metadata()
            else:
                # Ensure that any actions without a namespace are defined in IntegrationActionType
                if not cls.action_type in IntegrationActionType._value2member_map_:
                    raise ValueError(
                        f"action_type {cls.action_type} must be a valid IntegrationActionType"
                    )

            cls._all_types[cls.action_type] = cls
        else:
            cls.validate_metadata()

    @classmethod
    def validate_metadata(cls):
        """
        Validates that the action accepts the correct arguments and returns the correct return type.
        """
        if not cls.metadata:
            return

        func_type_hints = get_type_hints(cls.execute)
        args_type = func_type_hints.get("args")
        return_type = func_type_hints.get("return")
        cls.metadata.validate_args_type(args_type)
        if cls.entitlement and "mdr" in cls.entitlement:
            cls.validate_mdr_action()

        cls.metadata.validate_result_type(return_type)

    @classmethod
    def validate_mdr_action(cls):
        """
        Validates that the action metadata is correctly configured for the Portal TAP framework.

        This method ensures that actions have a result type that is a subclass of `TAPResult`.
        It also excludes certain predefined actions from this validation.
        """
        from apps.connectors.integrations.schemas import TAPResult

        # List of action types that are excluded from the validation
        # because they are not implemented in the Portal TAP framework.
        non_tap_actions = (
            list(FetchIntegrationActionType)
            + list(AlertIntegrationActionType)
            + list(IOCIntegrationActionType)
            + [
                UserIntegrationActionType.GET_EXTERNAL_USER_PROFILE_LINK,
            ]
        )

        def validate_result_type():
            if issubclass(cls.metadata.result_type, TAPResult):
                return True
            origin = get_origin(cls.metadata.result_type)
            if origin is Union or origin is UnionType:
                for typ in get_args(cls.metadata.result_type):
                    if issubclass(typ, TAPResult):
                        return True
            return False

        if cls.action_type not in non_tap_actions:
            if not validate_result_type():
                raise ValueError(
                    f"Action result type {cls.metadata.result_type} must be a "
                    f"subclass of {TAPResult.__name__}"
                )

    @classmethod
    def get_name(cls, action_type):
        return cls._all_types[action_type].name

    @classmethod
    def get_all_action_types(cls) -> list["IntegrationAction"]:
        return list(cls._all_types.values())

    @classmethod
    def get_public_invocable_actions(cls) -> list["IntegrationAction"]:
        return [
            action
            for action in cls.get_all_action_types()
            if action.invocation_type == InvocationType.PUBLIC
        ]

    @abstractmethod
    def execute(self, *args, **kwargs) -> Any:
        ...

    def poll(self, poll_context: IntegrationActionPollingContext):  # pragma: no cover
        raise NotImplementedError(f"poll not implemented for {self.__class__.__name__}")

    @abstractmethod
    def get_permission_checks(
        self, *args, **kwargs
    ) -> Sequence[type[IntegrationPermissionsHealthCheck]]:
        ...

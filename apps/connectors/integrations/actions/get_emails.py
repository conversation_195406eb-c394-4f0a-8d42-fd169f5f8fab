from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas.query_args import (
    MailMessageQueryArgs,
    UserQueryArgs,
)
from apps.connectors.integrations.schemas.query_result import EmailActivityQueryResult


class GetSimilarEmails(IntegrationAction):
    name = "Get Similar Emails"
    action_type = IntegrationActionType.GET_SIMILAR_EMAILS
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=MailMessageQueryArgs,
        result_type=EmailActivityQueryResult,
    )


class GetEmailActivityBySender(IntegrationAction):
    name = "Get Email Activity by Sender"
    action_type = IntegrationActionType.GET_EMAIL_ACTIVITY_BY_SENDER
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UserQueryArgs,
        result_type=EmailActivityQueryResult,
    )

from pydantic import BaseModel, Field

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    TAPResult,
    UPNIdentifierArgs,
    UserIdentifierArgs,
)


class RevokeUserSessionsStatus(BaseModel):
    revoked: bool = Field(
        description="Indicates whether the user's sessions were revoked.",
        default=False,
    )


class RevokeUserSessionsResult(TAPResult[RevokeUserSessionsStatus]):
    ...


class RevokeUserSessions(IntegrationAction):
    name = "Revoke user sessions"
    action_type = IntegrationActionType.REVOKE_USER_SESSIONS
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UserIdentifierArgs,
        result_type=RevokeUserSessionsResult,
    )


class RevokeUserSessionsByUPN(IntegrationAction):
    name = "Revoke user sessions by UPN"
    action_type = IntegrationActionType.REVOKE_USER_SESSIONS_BY_UPN
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UPNIdentifierArgs,
        result_type=RevokeUserSessionsResult,
    )

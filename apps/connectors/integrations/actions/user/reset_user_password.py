from pydantic import BaseModel, Field

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    TAPResult,
    UPNIdentifierArgs,
    UserIdentifierArgs,
)


class ResetUserPasswordStatus(BaseModel):
    reset: bool = Field(
        description="Indicates whether the user's password was reset.",
        default=False,
    )


class ResetUserPasswordResult(TAPResult[ResetUserPasswordStatus]):
    ...


class ResetUserPassword(IntegrationAction):
    name = "Reset user password"
    action_type = IntegrationActionType.RESET_USER_PASSWORD
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UserIdentifierArgs,
        result_type=ResetUserPasswordResult,
    )


class ResetUserPasswordByUPN(IntegrationAction):
    name = "Reset user password by UPN"
    action_type = IntegrationActionType.RESET_USER_PASSWORD_BY_UPN
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UPNIdentifierArgs,
        result_type=ResetUserPasswordResult,
    )

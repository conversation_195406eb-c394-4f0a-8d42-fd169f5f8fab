from enum import StrEnum

from criticalstart.fastapi_utils import OptionalParam
from pydantic import BaseModel, Field

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    TAPResult,
    UPNIdentifierArgs,
    UserIdentifierArgs,
)


class RiskyUserState(StrEnum):
    CONFIRMED = "confirmedCompromised"
    DISMISSED = "dismissed"


class RiskyUserStatus(BaseModel):
    risk_state: OptionalParam[RiskyUserState] = Field(
        description="State of the risky user.",
        default=None,
    )
    is_processing: bool = Field(
        description="Indicates whether user's risk state is still processing.",
        default=False,
    )


class RiskyUserResult(TAPResult[RiskyUserStatus]):
    ...


risky_user_metadata = IntegrationActionMetadata(
    args_type=UserIdentifierArgs,
    result_type=RiskyUserResult,
)

risky_user_by_upn_metadata = IntegrationActionMetadata(
    args_type=UPNIdentifierArgs,
    result_type=RiskyUserResult,
)


class ConfirmRiskyUser(IntegrationAction):
    name = "Confirm risky user"
    action_type = IntegrationActionType.CONFIRM_RISKY_USER
    entitlement = Entitlement.mdr
    metadata = risky_user_metadata


class ConfirmRiskyUserByUPN(IntegrationAction):
    name = "Confirm risky user by UPN"
    action_type = IntegrationActionType.CONFIRM_RISKY_USER_BY_UPN
    entitlement = Entitlement.mdr
    metadata = risky_user_by_upn_metadata


class DismissRiskyUser(IntegrationAction):
    name = "Dismiss risky user"
    action_type = IntegrationActionType.DISMISS_RISKY_USER
    entitlement = Entitlement.mdr
    metadata = risky_user_metadata


class DismissRiskyUserByUPN(IntegrationAction):
    name = "Dismiss risky user by UPN"
    action_type = IntegrationActionType.DISMISS_RISKY_USER_BY_UPN
    entitlement = Entitlement.mdr
    metadata = risky_user_by_upn_metadata

from enum import StrEnum
from typing import Optional

from pydantic import (
    BaseModel,
    Field,
)

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
    InvocationType,
)
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs


class Model(StrEnum):
    MODEL_GPT_3_5_TURBO = "MODEL_GPT_3_5_TURBO"
    MODEL_GPT_3_5_TURBO_16K = "MODEL_GPT_3_5_TURBO_16K"
    MODEL_GPT_4 = "MODEL_GPT_4"
    MODEL_GPT_4_32K = "MODEL_GPT_4_32K"
    MODEL_CURIE = "MODEL_CURIE"


class AiCompletionResponse(BaseModel):
    response: str
    total_tokens: int
    prompt_tokens: int
    completion_tokens: int


class AiCompletionArgs(IntegrationActionArgs):
    model: Model = Field(description="The model to use for completion")
    deployment_name: str
    system_prompt_text: Optional[str] = None
    prompt_text: str


class AiCompletion(IntegrationAction):
    name = "completion response"
    action_type = IntegrationActionType.AI_COMPLETION
    entitlement = Entitlement.internal_system
    invocation_type = InvocationType.PUBLIC
    metadata = IntegrationActionMetadata(
        args_type=AiCompletionArgs,
        result_type=AiCompletionResponse,
    )

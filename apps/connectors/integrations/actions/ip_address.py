from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import (
    IntegrationIdentifierArgs,
    IpAddressIdentifier,
    Message,
    TAPResult,
)


class BlockIpAddressArgs(IntegrationIdentifierArgs):
    ip_address: IpAddressIdentifier


class BlockIpAddressResult(TAPResult[Message]):
    ...


class BlockIpAddress(IntegrationAction):
    name = "Block IP Address"
    action_type = IntegrationActionType.BLOCK_IP_FIREWALL
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=BlockIpAddressArgs,
        result_type=BlockIpAddressResult,
    )


class UnblockIpAddressArgs(IntegrationIdentifierArgs):
    ip_address: IpAddressIdentifier


class UnblockIpAddressResult(TAPResult[Message]):
    ...


class UnblockIpAddress(IntegrationAction):
    name = "Unblock IP Address"
    action_type = IntegrationActionType.UNBLOCK_IP_FIREWALL
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UnblockIpAddressArgs,
        result_type=UnblockIpAddressResult,
    )


class QuarantineIpAddressArgs(IntegrationIdentifierArgs):
    ip_address: IpAddressIdentifier


class QuarantineIpAddressResult(TAPResult[Message]):
    ...


class QuarantineIpAddress(IntegrationAction):
    name = "Quarantine IP Address"
    action_type = IntegrationActionType.QUARANTINE_IP_FIREWALL
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=QuarantineIpAddressArgs,
        result_type=QuarantineIpAddressResult,
    )


class UnquarantineIpAddressArgs(IntegrationIdentifierArgs):
    ip_address: IpAddressIdentifier


class UnquarantineIpAddressResult(TAPResult[Message]):
    ...


class UnquarantineIpAddress(IntegrationAction):
    name = "Unquarantine IP Address"
    action_type = IntegrationActionType.UNQUARANTINE_IP_FIREWALL
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UnquarantineIpAddressArgs,
        result_type=UnquarantineIpAddressResult,
    )

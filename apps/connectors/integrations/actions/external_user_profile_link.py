from pydantic import BaseModel, Field

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
    InvocationType,
)
from apps.connectors.integrations.schemas.identifiers import UserIdentifierArgs


class ExternalUserProfileLinkResult(BaseModel):
    template: str = Field(
        description="The template to generate the external user profile link."
    )


class ExternalUserProfileLink(IntegrationAction):
    name = "Get external user profile link"
    action_type = IntegrationActionType.GET_EXTERNAL_USER_PROFILE_LINK
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UserIdentifierArgs,
        result_type=ExternalUserProfileLinkResult,
    )
    invocation_type = InvocationType.INTERNAL

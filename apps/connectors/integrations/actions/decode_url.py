from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas import TAPResult, ocsf
from apps.connectors.integrations.schemas.action_args import IntegrationIdentifierArgs
from apps.connectors.integrations.schemas.identifiers import UrlIdentifier


class DecodeUrlArgs(IntegrationIdentifierArgs):
    url: UrlIdentifier


class DecodeUrlResult(TAPResult[ocsf.Url]):
    ...


class DecodeUrl(IntegrationAction):
    name = "Decode URL"
    action_type = IntegrationActionType.DECODE_URL
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=DecodeUrlArgs,
        result_type=DecodeUrlResult,
    )

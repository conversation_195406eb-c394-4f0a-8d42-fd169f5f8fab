import ipaddress
import logging
from collections.abc import Generator
from datetime import datetime, timezone
from functools import wraps
from typing import Optional

from django.utils.dateparse import parse_datetime
from django.utils.timezone import now
from pydantic import BaseModel, ValidationError
from pydantic_extra_types.mac_address import MacAddress

logger = logging.getLogger(__name__)


class Network(BaseModel):
    mac_address: MacAddress


class NormalizationGenerator(Generator):
    def __init__(self, generator, normalizer):
        self.generator = generator
        self.normalizer = normalizer
        self.on_error = None
        self.ix = 0

    def send(self, value, /):  # pragma: no cover
        pass

    def throw(self, typ, val=None, tb=None, /):  # pragma: no cover
        pass

    def __iter__(self):  # pragma: no cover
        return self

    def __next__(self):
        # Continue processing until a normalized asset is returned
        while True:
            raw_obj = next(self.generator)
            try:
                self.ix += 1
                return self.normalizer(raw_obj)
            except (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Attribute<PERSON>rror, ValueError) as ex:
                if self.on_error:
                    self.on_error(ex, self.ix)

                logger.warning(
                    "Failed to normalize raw data",
                    exc_info=True,
                    extra={"item": raw_obj},
                )


def normalize(normalizer):
    """
    Decorator to normalize raw assets before yielding them.

    The wrapped function should yield one raw assets at a time.
    The normalizer function should always return a normalized asset.

    If the normalizer function raises an exception, the raw asset will be logged
    and the next raw asset will be processed.
    """

    def decorator(fn):
        @wraps(fn)
        def _wrapped(*args, **kwargs):
            return NormalizationGenerator(fn(*args, **kwargs), normalizer)

        return _wrapped

    return decorator


def parse_fqdn(fqdn: str) -> tuple[str, Optional[str]]:
    if fqdn is None:
        return "", None
    parts = fqdn.split(".", maxsplit=1)
    hostname = parts[0]
    domain = parts[1] if len(parts) == 2 else None
    return hostname, domain


def build_fqdn(hostname: Optional[str], domain_name: Optional[str]):
    if hostname and domain_name:
        return [".".join((hostname, domain_name))]
    return []


def to_list(value):
    """
    Convert a value to a list if it is not already a list.
    """
    if not value:
        return []
    if isinstance(value, list):
        return value
    return [value]


def normalize_mac_addresses(
    mac_addresses: list[str] | Optional[str],
) -> list[str]:
    if not isinstance(mac_addresses, list):
        mac_addresses = [mac_addresses] if mac_addresses else []
    validated_mac_address = []
    for mac_address in mac_addresses:
        if not mac_address:
            continue
        if len(mac_address) == 12:
            mac_address = ":".join(mac_address[i : i + 2] for i in range(0, 12, 2))
        try:
            model = Network(mac_address=mac_address)
        except ValidationError:
            continue
        else:
            mac_address = model.mac_address
        validated_mac_address.append(mac_address)
    return validated_mac_address


def normalize_ip_addresses(
    ip_addresses: list[str] | Optional[str],
) -> list[str]:
    if not isinstance(ip_addresses, list):
        ip_addresses = [ip_addresses] if ip_addresses else []
    validated_ip_addresses = []
    for ip_address in ip_addresses:
        try:
            ipaddress.ip_address(ip_address)
        except ValueError:
            continue
        validated_ip_addresses.append(ip_address)
    return validated_ip_addresses


def normalize_last_seen(
    datetime_strs: list[str | None] | str | None,
) -> datetime | None:
    """
    Normalize a list of datetime strings to a datetime object
    and return the maximum datetime object.
    """
    if not isinstance(datetime_strs, list):
        datetime_strs = [datetime_strs]

    max_allowed = now()  # Skip dates in the future
    iso_dates = []
    for dt_str in datetime_strs:
        if dt := parse_datetime(dt_str or ""):
            if not dt.tzinfo:
                dt = dt.replace(tzinfo=timezone.utc)
            dt = dt.replace(microsecond=0)
            if dt > max_allowed:
                continue
            iso_dates.append(dt)
    return max(iso_dates, default=None)

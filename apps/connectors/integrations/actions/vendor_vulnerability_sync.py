from datetime import datetime

from criticalstart.fastapi_utils import OptionalParam
from pydantic import BaseModel, Field

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs


class VendorVulnerabilitySyncArgs(IntegrationActionArgs):
    since: OptionalParam[datetime] = Field(
        description="Timestamp to sync data since. If not provided, sync all data.",
        default=None,
    )


class VendorVulnerability(BaseModel):
    source_id: str = Field(description="Vendor vulnerability ID.")
    technology_id: str = Field(description="Technology ID.")
    title: OptionalParam[str] = ""
    description: OptionalParam[str] = ""

    cvss_v3_base_score: OptionalParam[float] = None
    cvss_v2_base_score: OptionalParam[float] = None

    epss_score: OptionalParam[float] = None
    epss_percentile: OptionalParam[float] = None

    patchable: bool = False
    solution: OptionalParam[str] = ""

    cves: list[str] = []


class VendorVulnerabilitySync(IntegrationAction):
    name = "Fetch vendor vulnerabilities"
    action_type = IntegrationActionType.VENDOR_VULNERABILITY_SYNC
    entitlement = Entitlement.internal_system
    metadata = IntegrationActionMetadata(
        args_type=VendorVulnerabilitySyncArgs,
        result_type=VendorVulnerability,
    )

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas.query_args import (
    MailMessageQueryArgs,
    UrlQueryArgs,
)
from apps.connectors.integrations.schemas.query_result import NetworkActivityQueryResult


class GetUrlClicksByUrl(IntegrationAction):
    name = "Get URL Clicks by URL"
    action_type = IntegrationActionType.GET_URL_CLICKS_BY_URL
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=UrlQueryArgs,
        result_type=NetworkActivityQueryResult,
    )


class GetUrlClicksByMessage(IntegrationAction):
    name = "Get URL Clicks by Message"
    action_type = IntegrationActionType.GET_URL_CLICKS_BY_MESSAGE
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=MailMessageQueryArgs,
        result_type=NetworkActivityQueryResult,
    )


class GetUrlClicksByMessageSender(IntegrationAction):
    name = "Get URL Clicks by Message Sender"
    action_type = IntegrationActionType.GET_URL_CLICKS_BY_MESSAGE_SENDER
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=MailMessageQueryArgs,
        result_type=NetworkActivityQueryResult,
    )

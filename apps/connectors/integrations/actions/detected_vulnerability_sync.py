from datetime import datetime
from enum import StrEnum, auto

from criticalstart.fastapi_utils import OptionalParam
from pydantic import BaseModel, Field, model_validator

from apps.accounts import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
    InvocationType,
)
from apps.connectors.integrations.actions.host_sync import Host
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs


class DetectedVulnerabilityStatus(StrEnum):
    OPEN = auto()
    REOPENED = auto()
    FIXED = auto()


class DetectedVulnerabilityConfidenceLevel(StrEnum):
    INFORMATIONAL = auto()
    POTENTIAL = auto()
    CONFIRMED = auto()


class DetectedVulnerabilityKernelState(StrEnum):
    NON_KERNEL_RELATED = auto()
    RUNNING_KERNEL = auto()
    NON_RUNNING_KERNEL = auto()


class DetectedVulnerabilityServiceState(StrEnum):
    NON_SERVICE_RELATED = auto()
    RUNNING_SERVICE = auto()
    NON_RUNNING_SERVICE = auto()


class DetectedVulnerabilityConfigurationState(StrEnum):
    NON_CONFIGURATION_RELATED = auto()
    EXPLOITABLE_CONFIGURATION = auto()
    NON_EXPLOITABLE_CONFIGURATION = auto()


class DetectedVulnerabilitySyncArgs(IntegrationActionArgs):
    since: OptionalParam[datetime] = Field(
        description="Timestamp to sync data since. If not provided, sync all data.",
        default=None,
    )


class DetectedVulnerability(BaseModel):
    source_id: OptionalParam[str] = Field(
        description="Detected vulnerability ID.", default=""
    )
    technology_id: str = Field(description="Technology ID.")
    vendor_vulnerability_source_id: OptionalParam[str] = Field(
        description="Vendor vulnerability source ID.", default=""
    )
    first_seen_at: OptionalParam[datetime] = None
    last_activated_at: OptionalParam[datetime] = None
    last_seen_at: OptionalParam[datetime] = None

    active: bool = False
    status: DetectedVulnerabilityStatus = DetectedVulnerabilityStatus.OPEN

    confidence_level: DetectedVulnerabilityConfidenceLevel = (
        DetectedVulnerabilityConfidenceLevel.CONFIRMED
    )

    kernel_state: DetectedVulnerabilityKernelState = (
        DetectedVulnerabilityKernelState.NON_KERNEL_RELATED
    )
    service_state: DetectedVulnerabilityServiceState = (
        DetectedVulnerabilityServiceState.NON_SERVICE_RELATED
    )
    configuration_state: DetectedVulnerabilityConfigurationState = (
        DetectedVulnerabilityConfigurationState.NON_CONFIGURATION_RELATED
    )

    patch_superseded: bool = False

    asset: Host

    @model_validator(mode="after")
    def set_default_source_id(self):
        """
        Some integrations do not provide a source_id in the API response,
        so we generate a unique source_id based on the asset.source_id
        and the vendor_vulnerability_source_id.
        """
        if not self.source_id:  # pragma no cover
            self.source_id = (
                f"{self.asset.source_id}+{self.vendor_vulnerability_source_id}"
            )
        return self


class DetectedVulnerabilitySync(IntegrationAction):
    name = "Fetch detected vulnerabilities"
    action_type = IntegrationActionType.DETECTED_VULNERABILITY_SYNC
    entitlement = Entitlement.vulnerabilities
    invocation_type = InvocationType.PUBLIC
    metadata = IntegrationActionMetadata(
        args_type=DetectedVulnerabilitySyncArgs,
        result_type=DetectedVulnerability,
    )

from datetime import datetime
from typing import Any, List, Optional

from pydantic import BaseModel

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs


class EventIOCInfo(BaseModel):
    external_id: str
    external_name: str
    has_ioc_definition: bool
    mitre_techniques: Optional[List[str]]


class VendorRef(BaseModel):
    # FIXME: implement for all relevant integrations and make it required
    tenant_id: Optional[str] = None
    type: Optional[str] = None

    id: str


class VendorRefExtended(VendorRef):
    title: str
    alternate_id: Optional[str] = None
    url: Optional[str] = None
    created: Optional[datetime] = None


class Event(BaseModel):
    raw_event: dict
    event_timestamp: datetime
    ioc: Optional[EventIOCInfo] = None
    source_integration_id: Optional[str] = None
    vendor_item_ref: Optional[VendorRefExtended] = None
    vendor_parent_refs: Optional[List[VendorRef]] = None
    vendor_child_refs: Optional[List[VendorRef]] = None

    # vendor_group_ref is used to support the "VendorLifecycleGroup" feature.
    # The intent is to deprecate that implementation in favor of the
    # more robust vendor_item_ref and vendor_parent_refs implementation.
    # Remove this field when the feature is fully deprecated.
    vendor_group_ref: Optional[VendorRefExtended] = None

    ocsf: Optional[Any] = None  # Any was required for serialization

    def serialize(self):
        # The exclude_unset parameter is used in the serialize method to avoid including
        # fields that have not been set. This is particularly useful for the ocsf data,
        # which have a ton of optional fields.
        return self.model_dump(mode="json", by_alias=True, exclude_unset=True)


class EventSyncArgs(IntegrationActionArgs):
    ...


class EventSync(IntegrationAction):
    name = "Fetch Events"
    action_type = IntegrationActionType.EVENT_SYNC
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=EventSyncArgs,
        result_type=Event,
    )


class EventSyncFromArtifactArgs(IntegrationActionArgs):
    artifact_id: str


class EventSyncFromArtifact(IntegrationAction):
    name = "Fetch Events"
    action_type = IntegrationActionType.EVENT_SYNC_FROM_ARTIFACT
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=EventSyncFromArtifactArgs,
        result_type=Event,
    )

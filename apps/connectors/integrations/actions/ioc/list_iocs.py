from typing import List, Optional

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.actions.ioc.schemas.ioc import IOC
from apps.connectors.integrations.schemas.action_args import IntegrationActionArgs


class ListIocsArgs(IntegrationActionArgs):
    source_ids: Optional[List[str]] = None


class ListIocs(IntegrationAction):
    name = "List IOCs"
    action_type = IntegrationActionType.LIST_IOCS
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=ListIocsArgs,
        result_type=IOC,
    )

from pydantic import BaseModel

from apps.accounts.constants import Entitlement
from apps.connectors.integrations.actions.action import (
    IntegrationAction,
    IntegrationActionMetadata,
    IntegrationActionType,
)
from apps.connectors.integrations.actions.ioc.schemas.ioc import IOC


class SaveIocResult(BaseModel):
    source_id: str


class SaveIoc(IntegrationAction):
    name = "Save IOC"
    action_type = IntegrationActionType.SAVE_IOC
    entitlement = Entitlement.mdr
    metadata = IntegrationActionMetadata(
        args_type=IOC,
        result_type=SaveIocResult,
    )

from typing import ClassVar, Optional

from pydantic import Field

from apps.connectors.integrations.schemas.ocsf.classes.base_event import BaseEvent
from apps.connectors.integrations.schemas.ocsf.enums import Category
from apps.connectors.integrations.schemas.ocsf.objects import NetworkEndpoint
from apps.connectors.integrations.schemas.ocsf.objects.network_connection_info import (
    NetworkConnectionInfo,
)


class BaseNetwork(BaseEvent):
    """
    Hidden class for the BaseNetwork event type.
    """

    category: ClassVar = Category.NETWORK_ACTIVITY

    app_name: Optional[str] = Field(
        default=None,
        title="Application Name",
        description="The name of the application.",
    )
    dst_endpoint: Optional[NetworkEndpoint] = Field(
        default=None,
        title="Destination Endpoint",
        description="The responder (server) in a network connection.",
    )
    src_endpoint: Optional[NetworkEndpoint] = Field(
        default=None,
        title="Source Endpoint",
        description="The initiator (client) of the network connection.",
    )
    connection_info: Optional[NetworkConnectionInfo] = Field(
        default=None,
        description="The connection information.",
    )

from typing import ClassVar, Optional

from pydantic import Field

from apps.connectors.integrations.schemas.ocsf.enums import (
    NetworkActivityClass,
    NetworkActivityType,
)
from apps.connectors.integrations.schemas.ocsf.objects import NetworkTraffic, Url

from .base_network import BaseNetwork


class NetworkActivity(BaseNetwork):
    ocsf_class: ClassVar = NetworkActivityClass.NETWORK_ACTIVITY
    activity_enum: ClassVar = NetworkActivityType
    traffic: Optional[NetworkTraffic] = Field(
        default=None,
        description="The network traffic object.",
    )
    url: Optional[Url] = Field(
        default=None,
        description="The URL object.",
    )

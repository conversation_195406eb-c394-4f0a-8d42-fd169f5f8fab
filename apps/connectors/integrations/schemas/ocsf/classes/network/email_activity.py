from typing import ClassVar, Optional

from pydantic import Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    EmailActivityDirection,
    EmailActivityType,
    NetworkActivityClass,
)
from apps.connectors.integrations.schemas.ocsf.objects import Email, EmailAuthentication

from .base_network import BaseNetwork

# https://schema.ocsf.io/1.4.0/classes/email_activity


class EmailActivity(BaseNetwork):
    ocsf_class: ClassVar = NetworkActivityClass.EMAIL_ACTIVITY
    activity_enum: ClassVar[EmailActivityType] = EmailActivityType

    @model_validator(mode="before")
    def _set_email_activity_enum_fields(cls, values):
        EmailActivityDirection.set_values(values, "direction_id", "direction")
        return values

    attempt: Optional[int] = Field(
        default=None,
        description="The attempt number for attempting to deliver the email.",
    )
    banner: Optional[str] = Field(
        default=None,
        description="The initial connection response that a messaging server receives "
        "after it connects to an email server.",
    )
    command: Optional[str] = Field(
        default=None,
        description="The command issued by the initiator (client), such as SMTP HELO or EHLO.",
    )
    direction: Optional[str] = Field(
        default=None,
        description="The direction of the email, as defined by the direction_id value.",
    )
    direction_id: int = Field(
        default=None,
        description="""
        The direction of the email relative to the scanning host or organization.

        Email scanned at an internet gateway might be characterized as inbound to the
        organization from the Internet, outbound from the organization to the Internet,
        or internal within the organization. Email scanned at a workstation might be
        characterized as inbound to, or outbound from the workstation.
        0	Unknown
            The email direction is unknown.
        1	Inbound
            Email Inbound, from the Internet or outside network destined for an
            entity inside network.
        2	Outbound
            Email Outbound, from inside the network destined for an entity outside network.
        3	Internal
            Email Internal, from inside the network destined for an entity inside network.
        99	Other
            The direction is not mapped. See the direction attribute, which contains a
            data source specific value.
        """,
    )
    email: Email = Field(
        ...,
        description="The email object.",
    )
    email_auth: Optional[EmailAuthentication] = Field(
        default=None,
        description="The SPF, DKIM and DMARC attributes of an email.",
    )
    message_trace_uid: Optional[str] = Field(
        default=None,
        description="The identifier that tracks a message that travels through "
        "multiple points of a messaging service.",
    )
    protocol_name: Optional[str] = Field(
        default=None,
        description="The Protocol Name specifies the email communication protocol, "
        "such as SMTP, IMAP, or POP3.",
    )

from typing import ClassVar, Optional

from pydantic import Field, model_validator

from apps.connectors.integrations.schemas.ocsf.classes.base_event import BaseEvent
from apps.connectors.integrations.schemas.ocsf.enums import Category, Confidence
from apps.connectors.integrations.schemas.ocsf.profiles import IncidentProfile


class BaseFinding(IncidentProfile, BaseEvent):
    """
    Hidden class for the BaseFinding event type.
    """

    category: ClassVar = Category.FINDINGS

    @model_validator(mode="before")
    def _set_finding_fields(cls, values):
        Confidence.set_values(values, "confidence_id", "confidence")

        return values

    confidence: Optional[str] = Field(
        default=None,
        description="""
        The confidence, normalized to the caption of the confidence_id value.
        """,
    )
    confidence_id: Optional[int] = Field(
        default=None,
        description="""
        The normalized confidence ID, which refers to the accuracy of the rule
        that created the finding.
        """,
    )
    confidence_score: Optional[int] = Field(
        default=None,
        description="""
        The confidence score as reported by the event source.
        """,
    )
    comment: Optional[str] = Field(
        default=None,
        description="""
        A comment associated with the finding.
        """,
    )

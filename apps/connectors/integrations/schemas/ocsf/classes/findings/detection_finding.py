from typing import ClassVar, List, Optional

from pydantic import Field

from apps.connectors.integrations.schemas.ocsf.enums import (
    DetectionActivity,
    DetectionStatus,
    Findings,
)
from apps.connectors.integrations.schemas.ocsf.objects import (
    EvidenceArtifacts,
    FindingInformation,
    Remediation,
    ResourceDetails,
)
from apps.connectors.integrations.schemas.ocsf.objects.cloud import Cloud
from apps.connectors.integrations.schemas.ocsf.objects.policy import Policy

from .base_finding import BaseFinding


class DetectionFinding(BaseFinding):
    ocsf_class: ClassVar = Findings.DETECTION_FINDING
    activity_enum: ClassVar = DetectionActivity
    event_status_enum: ClassVar = DetectionStatus

    evidences: list[EvidenceArtifacts] = Field(
        default_factory=list,
        description="""
        Describes various evidence artifacts associated to the activity/activities that
        triggered a security detection.
        """,
    )
    finding_info: FindingInformation = Field(
        description="""
        Describes the supporting information about a generated finding.
        """,
    )
    remediation: Optional[Remediation] = Field(
        default=None,
        description="""
        Describes the recommended remediation steps to address identified
        issues.
        """,
    )
    resources: Optional[List[ResourceDetails]] = Field(
        default=None,
        description="""
        Describes details about resources that were the target of the activity
        that triggered the finding.
        """,
    )
    policy: Optional[Policy] = Field(
        default=None,
        description="""
        The policy that pertains to the control that triggered the event.
        """,
    )
    cloud: Optional[Cloud] = Field(
        default=None,
        description="""
        The cloud account and provider associated with the event.
        """,
    )

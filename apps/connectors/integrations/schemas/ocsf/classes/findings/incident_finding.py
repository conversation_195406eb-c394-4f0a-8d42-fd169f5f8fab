from datetime import datetime
from typing import ClassVar, List, Optional

from pydantic import Field

from apps.connectors.integrations.schemas.ocsf.enums import (
    Findings,
    IncidentActivity,
    IncidentStatus,
)
from apps.connectors.integrations.schemas.ocsf.objects import (
    FindingInformation,
    MitreAttack,
    VendorAttributes,
)

from .base_finding import BaseFinding


class IncidentFinding(BaseFinding):
    ocsf_class: ClassVar = Findings.INCIDENT_FINDING
    activity_enum: ClassVar = IncidentActivity
    status_enum: ClassVar = IncidentStatus

    attacks: Optional[List[MitreAttack]] = Field(
        default=None,
        description="""
        The MITRE ATT&CK tactics and techniques associated with the finding.
        """,
    )
    desc: str = Field(
        default_factory=str,
        description="""
        The description of the finding.
        """,
    )
    end_time_dt: Optional[datetime] = Field(
        default=None,
        description="""
        The end time of the finding.
        """,
    )
    """
    Added by Date/Time profile
    """

    finding_info_list: Optional[list[FindingInformation]] = Field(
        default=None,
        description="""
        Describes the supporting information about a generated finding.
        """,
    )
    vendor_attributes: Optional[VendorAttributes] = Field(
        default=None,
        description="""
        The vendor attributes of the finding.
        """,
    )

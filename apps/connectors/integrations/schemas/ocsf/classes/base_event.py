from datetime import datetime
from typing import ClassVar, Optional

from pydantic import BaseModel, Field, computed_field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    EventStatus,
    OCSFEnum,
    Severity,
)
from apps.connectors.integrations.schemas.ocsf.objects import (
    Enrichment,
    Metadata,
    Observable,
)
from apps.connectors.integrations.schemas.ocsf.profiles import (
    HostProfile,
    OSINTProfile,
    SecurityControlProfile,
)


class BaseEvent(HostProfile, SecurityControlProfile, OSINTProfile, BaseModel):
    category: ClassVar[OCSFEnum]
    ocsf_class: ClassVar[OCSFEnum]
    activity_enum: ClassVar[type[OCSFEnum]]
    event_status_enum: ClassVar[type[OCSFEnum]] = EventStatus

    @model_validator(mode="before")
    def _set_enum_fields_base_event(cls, values):
        fields_set_by_enum = (
            "category_name",
            "category_uid",
            "class_name",
            "class_uid",
            "activity_id",
            "activity_name",
            "status_id",
            "severity_id",
        )
        if any(field in values for field in fields_set_by_enum):
            raise ValueError(
                "The following fields are automatically populated by the model and "
                "should not be provided manually: "
                f"{', '.join(field for field in fields_set_by_enum if field in values)}"
            )
        values["category_name"] = cls.category.name
        values["category_uid"] = cls.category.id
        values["class_name"] = cls.ocsf_class.name
        values["class_uid"] = cls.ocsf_class.id

        cls.activity_enum.set_values(
            values,
            "activity_id",
            "activity_name",
            enum_key="activity",
        )
        cls.event_status_enum.set_values(values, "status_id", "status")

        Severity.set_values(values, "severity_id", "severity")

        return values

    @model_validator(mode="after")
    def _calculate_fields_base_event(self):
        # OCSF type_uid is calculated as class_uid * 100 + activity_id
        self.type_uid = self.class_uid * 100 + self.activity_id
        self.type_name = f"{self.class_name}: {self.activity_name}"

        return self

    activity: OCSFEnum = Field(
        default=None,
        exclude=True,
        description="Hidden field to set activity_id and activity_name",
    )  # type: ignore

    activity_id: int = Field(
        default=None,
        description="""
        The normalized identifier of the finding activity.
        """,
    )  # type: ignore
    activity_name: Optional[str] = Field(
        default=None,
        description="""
        The finding activity name, as defined by the activity_id.
        """,
    )
    category_name: str = Field(
        default=None,
        description="""
        The event category name, as defined by category_uid value: Findings.
        """,
    )  # type: ignore
    """
    Set by ClassVar category
    """
    category_uid: int = Field(
        default=None,
        description="""
        The category unique identifier of the event.
        """,
    )  # type: ignore
    """
    Set by ClassVar category
    """
    class_name: str = Field(
        default=None,
        description="""
        The event class name, as defined by class_uid value.
        """,
    )  # type: ignore
    """
    Set by ClassVar ocsf_class
    """
    class_uid: int = Field(
        default=None,
        description="""
        The unique identifier of a class describing the event attributes.
        """,
    )  # type: ignore
    """
    Set by ClassVar ocsf_class
    """
    count: Optional[int] = Field(
        default=None,
        description="""
        The count of the finding.
        """,
    )
    end_time_dt: Optional[datetime] = Field(
        default=None,
        description="""
        The time of the most recent event included in the finding.
        """,
    )
    enrichments: Optional[list[Enrichment]] = Field(
        default=None,
        description="""
            The enrichments associated with the finding.
            """,
    )
    message: str = Field(
        description="""
        The description of the event/finding, as defined by the source.
        """,
    )
    """
    The platform uses `message` for the event title.
    """

    metadata: Metadata = Field(
        description="""
        The metadata associated with the event or a finding.
        """,
    )
    observables: Optional[list[Observable]] = Field(
        default=None,
        description="""
        The observables associated with the event or a finding.
        """,
    )
    raw_data: Optional[str] = Field(
        default=None,
        description="""
        The raw data of the finding.
        """,
    )
    severity: Optional[str] = Field(
        default=None,
        description="""
        The event/finding severity, normalized to the caption of the severity_id value.
        In the case of 'Other', it is defined by the source.
        """,
    )
    severity_id: Optional[int] = Field(
        default=None,
        description="""
        The normalized identifier of the event/finding severity.

        The normalized severity is a measurement the effort and expense required to
        manage and resolve an event or incident. Smaller numerical values represent
        lower impact events, and larger numerical values represent higher impact events.
        0	Unknown
            The event/finding severity is unknown.
        1	Informational
            Informational message. No action required.
        2	Low
            The user decides if action is needed.
        3	Medium
            Action is required but the situation is not serious at this time.
        4	High
            Action is required immediately.
        5	Critical
            Action is required immediately and the scope is broad.
        6	Fatal
            An error occurred but it is too late to take remedial action.
        99	Other
            The event/finding severity is not mapped. See the severity attribute,
            which contains a data source specific value.
        """,
    )
    start_time_dt: Optional[datetime] = Field(
        default=None,
        description="""
        The time of the least recent event included in the finding.
        """,
    )
    """
    Added by Date/Time profile
    """

    status: Optional[str] = Field(
        default=None,
        description="""
        The normalized status of the Finding set by the consumer, normalized
        to the caption of the status_id value. In the case of 'Other', it is
        defined by the source.
        """,
    )
    status_id: Optional[int] = Field(
        default=None,
        description="""
        The normalized status identifier of the Finding, set by the consumer.
        """,
    )
    status_code: Optional[str] = Field(
        default=None,
        description="""
        The event status code, as reported by the event source.

        For example, in a Windows Failed Authentication event, this would
        be the value of 'Failure Code', e.g. 0x18.
        """,
    )
    status_detail: Optional[str] = Field(
        default=None,
        description="""
        The status detail contains additional information about the event/finding outcome.
        """,
    )

    @computed_field
    def time(self) -> int:
        """
        The normalized event occurrence time or the finding creation time.
        """
        return int(self.time_dt.timestamp() * 1000)

    time_dt: datetime = Field(
        default=None,
        description="""
        The normalized event occurrence time or the finding creation time.
        """,
    )  # type: ignore
    """
    Added by Date/Time profile and required to auto-set time
    """

    type_name: Optional[str] = Field(
        default=None,
        description="""
        The event/finding type name, as defined by the type_uid.
        """,
    )
    """
    Calculated by model validator
    """

    type_uid: Optional[int] = Field(
        default=None,
        description="""
        The event/finding type ID. It identifies the event's semantics and
        structure. The value is calculated by the logging system as:
        class_uid * 100 + activity_id.
        """,
    )
    """
    Calculated by model validator
    """
    unmapped: Optional[object] = Field(
        default=None,
        description="""
        The unmapped attributes of the finding.
        """,
    )

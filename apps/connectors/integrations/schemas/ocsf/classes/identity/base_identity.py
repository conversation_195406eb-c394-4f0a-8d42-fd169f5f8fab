from typing import ClassVar, Optional

from pydantic import Field

from apps.connectors.integrations.schemas.ocsf.classes.base_event import BaseEvent
from apps.connectors.integrations.schemas.ocsf.enums import Category
from apps.connectors.integrations.schemas.ocsf.objects.http_request import HttpRequest


class BaseIdentity(BaseEvent):
    """
    Hidden class for the BaseIdentity event type.
    """

    category: ClassVar = Category.IDENTITY_AND_ACCESS_MANAGEMENT

    http_request: Optional[HttpRequest] = Field(
        default=None,
        title="HTTP Request",
        description="Details about the underlying HTTP request.",
    )

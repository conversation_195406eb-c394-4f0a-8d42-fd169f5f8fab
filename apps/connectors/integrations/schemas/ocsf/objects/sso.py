from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import AuthProtocol

from .certificate import DigitalCertificate


class SSO(BaseModel):
    """
    The Single Sign-On (SSO) object provides a structure for normalizing SSO
    attributes, configuration, and/or settings from Identity Providers.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        AuthProtocol.set_values(values, "auth_protocol_id", "auth_protocol")
        return values

    auth_protocol: Optional[str] = Field(
        default=None,
        title="Auth Protocol",
        description="""
        The authorization protocol as defined by the caption of `auth_protocol_id`. In the
        case of `Other`, it is defined by the event source.
        """,
    )
    auth_protocol_id: Optional[int] = Field(
        default=None,
        title="Auth Protocol ID",
        description="""
        The normalized identifier of the authentication protocol used by the SSO resource.

        0    Unknown: The authentication protocol is unknown.
        1    NTLM
        2    Kerberos
        3    Digest
        4    OpenID
        5    SAML
        6    OAUTH 2.0
        7    PAP
        8    CHAP
        9    EAP
        10   RADIUS
        11   Basic Authentication
        12   LDAP
        99   Other: The authentication protocol is not mapped. See the `auth_protocol`
              attribute, which contains a data source specific value.
        """,
    )
    certificate: Optional[DigitalCertificate] = Field(
        default=None,
        title="SAML Certificate",
        description="""
        Digital Signature associated with the SSO resource, e.g., SAML X.509 certificate
        details.
        """,
    )
    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        When the SSO resource was created.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        When the SSO resource was created.
        """,
    )
    duration_mins: Optional[int] = Field(
        default=None,
        title="SSO Session Duration",
        description="""
        The duration (in minutes) for an SSO session, after which re-authentication is
        required.
        """,
    )
    idle_timeout: Optional[int] = Field(
        default=None,
        title="SSO Idle Timeout",
        description="""
        Duration (in minutes) of allowed inactivity before Single Sign-On (SSO) session
        expiration.
        """,
    )
    login_endpoint: Optional[str] = Field(
        default=None,
        title="SSO Login Endpoint",
        description="""
        URL for initiating an SSO login request.
        """,
    )
    logout_endpoint: Optional[str] = Field(
        default=None,
        title="SSO Logout Endpoint",
        description="""
        URL for initiating an SSO logout request, allowing sessions to be terminated across
        applications.
        """,
    )
    metadata_endpoint: Optional[str] = Field(
        default=None,
        title="SSO Metadata Endpoint",
        description="""
        URL where metadata about the SSO configuration is available (e.g., for SAML
        configurations).
        """,
    )
    modified_time: Optional[int] = Field(
        default=None,
        title="Modified Time",
        description="""
        The most recent time when the SSO resource was updated.
        """,
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="""
        The most recent time when the SSO resource was updated.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The name of the SSO resource.
        """,
    )
    protocol_name: Optional[str] = Field(
        default=None,
        title="Supported Protocol",
        description="""
        The supported protocol for the SSO resource. E.g., `SAML` or `OIDC`.
        """,
    )
    scopes: Optional[List[str]] = Field(
        default=None,
        title="Scopes",
        description="""
        Scopes define the specific permissions or actions that the client is allowed to perform
        on behalf of the user. Each scope represents a different set of permissions, and the
        user can selectively grant or deny access to specific scopes during the authorization
        process.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        A unique identifier for a SSO resource.
        """,
    )
    vendor_name: Optional[str] = Field(
        default=None,
        title="Service Provider",
        description="""
        Name of the vendor or service provider implementing SSO. E.g., `Okta`, `Auth0`,
        `Microsoft`.
        """,
    )

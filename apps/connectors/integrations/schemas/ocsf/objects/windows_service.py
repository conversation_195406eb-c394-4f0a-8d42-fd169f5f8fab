from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    ServiceCategory,
    ServiceErrorControl,
    ServiceStartType,
    ServiceType,
)

from .key_value_object import KeyValueObject


class WinService(BaseModel):
    """
    The Windows Service object describes a Windows service.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        ServiceCategory.set_values(values, "service_category_id", "service_category")
        ServiceErrorControl.set_values(
            values, "service_error_control_id", "service_error_control"
        )
        ServiceStartType.set_values(
            values, "service_start_type_id", "service_start_type"
        )
        ServiceType.set_values(values, "service_type_id", "service_type")
        return values

    cmd_line: Optional[str] = Field(
        default=None,
        title="Command Line",
        description="""The full command line used to launch the service.""",
    )
    labels: Optional[List[str]] = Field(
        default=None,
        title="Labels",
        description="""The list of labels associated with the service.""",
    )
    load_order_group: Optional[str] = Field(
        default=None,
        title="Load Order Group",
        description="""The name of the load ordering group of which this service is a
        member.""",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""The unique name of the service.""",
    )
    service_category: Optional[str] = Field(
        default=None,
        title="Service Category",
        description="""The service category, normalized to the caption of the
        service_category_id value. In the case of 'Other', it is defined by the
        event source.""",
    )
    service_category_id: Optional[int] = Field(
        default=None,
        title="Service Category ID",
        description="""
        The normalized identifier of the service category.
        0    Unknown: The service category is unknown.
        1    Kernel Mode: A kernel mode driver.
        2    User Mode: A user mode service.
        99   Other: The service category is not mapped.
        """,
    )
    service_dependencies: Optional[List[str]] = Field(
        default=None,
        title="Service Dependencies",
        description="""The names of other services upon which this service has a
        dependency.""",
    )
    service_error_control: Optional[str] = Field(
        default=None,
        title="Service Error Control",
        description="""The service error control, normalized to the caption of the
        `service_error_control_id` value. In the case of 'Other', it is defined by
        the event source.""",
    )
    service_error_control_id: Optional[int] = Field(
        default=None,
        title="Service Error Control ID",
        description="""
        The normalized identifier of the service error control.
        0    Unknown: The service error control is unknown.
        1    Ignore: The startup program ignores the error and continues the startup
            operation.
        2    Normal: The startup program logs the error in the event log but continues the
            startup operation.
        3    Severe: The startup program logs the error in the event log. If the
            last-known-good configuration is being started, the startup operation continues.
            Otherwise, the system is restarted with the last-known-good configuration.
        4    Critical: The startup program logs the error in the event log, if possible.
            If the last-known-good configuration is being started, the startup operation
            fails. Otherwise, the system is restarted with the last-known good configuration.
        99   Other: The service error control is not mapped.
        """,
    )
    service_start_name: Optional[str] = Field(
        default=None,
        title="Service Start Name",
        description="""For a user mode service, this attribute represents the name of
        the account under which the service is run. For a kernel mode driver, this
        attribute represents the object name used to load the driver.""",
    )
    service_start_type: Optional[str] = Field(
        default=None,
        title="Service Start Type",
        description="""The service start type, normalized to the caption of the
        `service_start_type_id` value. In the case of 'Other', it is defined by the
        event source.""",
    )
    service_start_type_id: Optional[int] = Field(
        default=None,
        title="Service Start Type ID",
        description="""
        The normalized identifier of the service start type.
        0    Unknown: The service start type is unknown.
        1    Boot: A kernel mode driver loaded at boot.
        2    System: A kernel mode driver loaded during system startup.
        3    Auto: A user mode service started automatically during system startup.
        4    Demand: A user mode service started on demand when a process calls StartService.
        5    Disabled: A driver or service that cannot be started.
        99   Other: The service start type is not mapped.
        """,
    )
    service_type: Optional[str] = Field(
        default=None,
        title="Service Type",
        description="""The service type, normalized to the caption of the
        `service_type_id` value. In the case of 'Other', it is defined by the event
        source.""",
    )
    service_type_id: Optional[int] = Field(
        default=None,
        title="Service Type ID",
        description="""
        The normalized identifier of the service type.
        0    Unknown: The service type is unknown.
        1    Kernel Driver: A kernel mode driver.
        2    File System Driver: A kernel mode file system minifilter.
        3    Own Process: A user mode service that runs in its own process.
        4    Share Process: A user mode service that shares a process with other services.
        99   Other: The service type is not mapped.
            """,
    )
    tags: Optional[List[KeyValueObject]] = Field(
        default=None,
        title="Tags",
        description="""The list of tags; `{key:value}` pairs associated to the
        service.""",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""The unique identifier of the service.""",
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="""The version of the service.""",
    )

from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import ScriptType

from .file import File, Fingerprint
from .long_string import LongString


class Script(BaseModel):
    """
    The Script object describes a script or command that can be executed by a shell,
    script engine, or interpreter. Examples include Bash, JavsScript, PowerShell,
    Python, VBScript, etc. Note that the term *script* here denotes not only a script
    contained within a file but also a script or command typed interactively by a
    user, supplied on the command line, or provided by some other file-less mechanism.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        ScriptType.set_values(values, "type_id", "type")
        return values

    file: Optional[File] = Field(
        default=None,
        title="File",
        description="""Present if this script is associated with a file. Not present in
        the case of a file-less script.""",
    )
    hashes: Optional[List[Fingerprint]] = Field(
        default=None,
        title="Hashes",
        description="""An array of the script's cryptographic hashes. Note that these
        hashes are calculated on the script in its original encoding, and not on the
        normalized UTF-8 encoding found in the `script_content` attribute.""",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""Unique identifier for the script or macro, independent of the
        containing file, used for tracking, auditing, and security analysis.""",
    )
    parent_uid: Optional[str] = Field(
        default=None,
        title="Parent Unique ID",
        description="""This attribute relates a sub-script to a parent script having
        the matching `uid` attribute. In the case of PowerShell, sub-script execution
        can be identified by matching the activity correlation ID of the raw ETW events
        provided by the OS.""",
    )
    script_content: Optional[LongString] = Field(
        default=None,
        title="Script Content",
        description="""The script content, normalized to UTF-8 encoding irrespective of
        its original encoding. When emitting this attribute, it may be appropriate to
        truncate large scripts. When consuming this attribute, large scripts should be
        anticipated.""",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""The script type, normalized to the caption of the `type_id`
        value. In the case of 'Other', it is defined by the event source.""",
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The normalized script type ID.

        0    Unknown: The script type is unknown.
        1    Windows Command Prompt
        2    PowerShell
        3    Python
        4    JavaScript
        5    VBScript
        6    Unix Shell
        7    VBA
        99   Other: The script type is not mapped.
            """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""Some script engines assign a unique ID to each individual
        execution of a given script. This attribute captures that unique ID. In the
        case of PowerShell, the unique ID corresponds to the `ScriptBlockId` in the
        raw ETW events provided by the OS.""",
    )

from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import TicketType


class Ticket(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        TicketType.set_values(values, "type_id", "type")

        return values

    src_url: str = Field(
        default_factory=str,
        description="""
            A Url link used to access the original incident.
            """,
    )
    title: Optional[str] = Field(
        default=None,
        description="""
            The title of the ticket.
            """,
    )
    type: Optional[str] = Field(
        default=None,
        description="""
            The linked ticket type determines whether the ticket is internal or in an
            external ticketing system.
            """,
    )
    type_id: Optional[int] = Field(
        default=None,
        description="""
        The normalized identifier for the ticket type.
        0	Unknown
            The type is unknown.
        1	Internal
        2	External
        99	Other
            The type is not mapped. See the type attribute, which contains a data source
            specific value.
        """,
    )
    uid: str = Field(
        default_factory=str,
        description="Unique ticket identifier like ticket id.",
    )

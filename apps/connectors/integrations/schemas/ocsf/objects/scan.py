from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import ScanType


class Scan(BaseModel):
    """
    The Scan object describes characteristics of a proactive scan.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        ScanType.set_values(values, "type_id", "type")
        return values

    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The administrator-supplied or application-generated name of the scan.
        For example: "Home office weekly user database scan", "Scan folders for viruses",
        "Full system virus scan"
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The type of scan.
        """,
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The type id of the scan.
        0    Unknown: The type is unknown.
        1    Manual: The scan was manually initiated by the user or administrator.
        2    Scheduled: The scan was started based on scheduler.
        3    Updated Content: The scan was triggered by a content update.
        4    Quarantined Items: The scan was triggered by newly quarantined items.
        5    Attached Media: The scan was triggered by the attachment of removable media.
        6    User Logon: The scan was started due to a user logon.
        7    ELAM: The scan was triggered by an Early Launch Anti-Malware (ELAM) detection.
        99   Other: The scan type id is not mapped. See the `type` attribute, which contains
             a data source specific value.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Scan UID",
        description="""
        The application-defined unique identifier assigned to an instance of a scan.
        """,
    )

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import SBOMType

from .package import SoftwarePackage
from .product import Product
from .software_component import SoftwareComponent


class SoftwareBillOfMaterials(BaseModel):
    """
    The Software Bill of Materials object describes characteristics of a generated SBOM.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        SBOMType.set_values(values, "type_id", "type")
        return values

    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the SBOM was created.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the SBOM was created.
        """,
    )
    package: Optional[SoftwarePackage] = Field(
        default=None,
        title="Software Package",
        description="""
        The software package or library that is being discovered or inventoried by an SBOM.
        """,
    )
    product: Optional[Product] = Field(
        default=None,
        title="Product",
        description="""
        Details about the upstream product that generated the SBOM e.g. `cdxgen` or `Syft`.
        """,
    )
    software_components: Optional[list[SoftwareComponent]] = Field(
        default=None,
        title="Software Components",
        description="""
        The list of software components used in the software package.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The type of SBOM, normalized to the caption of the `type_id` value. In the case of
        'Other', it is defined by the source.
        """,
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The type of SBOM.

        0    Unknown: The type is unknown.
        1    SPDX: System Package Data Exchange (SPDX®) is an open standard capable of
             representing systems with software components in SBOMs and other AI, data and
             security references supporting a range of risk management use cases. The SPDX
             specification is a freely available international open standard (ISO/IEC
             5692:2021).
        2    CycloneDX: CycloneDX is an International Standard for Bill of Materials
             (ECMA-424).
        3    SWID: The International Organization for Standardization (ISO) and the
             International Electrotechnical Commission (IEC) publishes, ISO/IEC 19770-2, a
             standard for software identification (SWID) tags that defines a structured
             metadata format for describing a software product. A SWID tag document is
             composed of a structured set of data elements that identify the software
             product.
        99   Other: The type is not mapped. See the `type` attribute, which contains a data
             source specific value.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="SBOM ID",
        description="""
        A unique identifier for the SBOM or the SBOM generation by a source tool, such as
        the SPDX `metadata.component.bom-ref`.
        """,
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="""
        The specification (spec) version of the particular SBOM, e.g., `1.6`.
        """,
    )

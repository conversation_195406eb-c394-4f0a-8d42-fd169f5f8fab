from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import FixCoverage

from .advisory import Advisory
from .affected_code import AffectedCode
from .cve import CVE
from .cwe import CWE
from .package import AffectedPackage
from .remediation import Remediation


class Vulnerability(BaseModel):
    """
    The vulnerability is an unintended characteristic of a computing component or
    system configuration that multiplies the risk of an adverse event or a loss
    occurring either due to accidental exposure, deliberate attack, or conflict with
    new system components.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        FixCoverage.set_values(values, "fix_coverage_id", "fix_coverage")
        return values

    advisory: Optional[Advisory] = Field(
        default=None,
        title="Security Advisory",
        description="""Detail about the security advisory, that is used to publicly disclose
        cybersecurity vulnerabilities by a vendor.""",
    )
    affected_code: Optional[List[AffectedCode]] = Field(
        default=None,
        title="Affected Code",
        description="""List of Affected Code objects that describe details about code blocks
        identified as vulnerable.""",
    )
    affected_packages: Optional[List[AffectedPackage]] = Field(
        default=None,
        title="Affected Software Packages",
        description="""List of software packages identified as affected by a
        vulnerability/vulnerabilities.""",
    )
    category: Optional[str] = Field(
        default=None,
        title="Category",
        description="""The category of a vulnerability or weakness, as reported by the source tool,
        such as Container Security or Open Source Security.""",
    )
    cve: Optional[CVE] = Field(
        default=None,
        title="CVE",
        description="""Describes the Common Vulnerabilities and Exposures (CVE) details related to the
        vulnerability.""",
    )
    cwe: Optional[CWE] = Field(
        default=None,
        title="CWE",
        description="""Describes the Common Weakness Enumeration (CWE) details related to the
        vulnerability.""",
    )
    dependency_chain: Optional[str] = Field(
        default=None,
        title="Dependency Chain",
        description="""Information about the chain of dependencies related to the issue as reported by
        an Application Security or Vulnerability Management tool. E.g., serverless-offline ->
        @serverless/utils -> memoizee -> es5-ext.""",
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""The description of the vulnerability.""",
    )
    exploit_last_seen_time: Optional[int] = Field(
        default=None,
        title="Exploit Last Seen Time",
        description="""The time when the exploit was most recently observed.""",
    )
    exploit_last_seen_time_dt: Optional[datetime] = Field(
        default=None,
        title="Exploit Last Seen Time",
        description="""The time when the exploit was most recently observed.""",
    )
    exploit_ref_url: Optional[str] = Field(
        default=None,
        title="Exploit URL",
        description="""The URL of the exploit code or Proof-of-Concept (PoC).""",
    )
    exploit_requirement: Optional[str] = Field(
        default=None,
        title="Exploit Requirement",
        description="""The requirement description related to any constraints around exploit
        execution.""",
    )
    exploit_type: Optional[str] = Field(
        default=None,
        title="Exploit Type",
        description="""The categorization or type of Exploit. E.g., Network or Physical.""",
    )
    first_seen_time: Optional[int] = Field(
        default=None,
        title="First Seen",
        description="""The time when the vulnerability was first observed.""",
    )
    first_seen_time_dt: Optional[datetime] = Field(
        default=None,
        title="First Seen",
        description="""The time when the vulnerability was first observed.""",
    )
    fix_coverage: Optional[str] = Field(
        default=None,
        title="Fix Coverage",
        description="""The fix coverage, normalized to the caption of the fix_coverage_id value.""",
    )
    fix_coverage_id: Optional[int] = Field(
        default=None,
        title="Fix Coverage ID",
        description="""The normalized identifier for fix coverage, applicable to this vulnerability.
        Typically useful, when there are multiple affected packages but only a subset have
        available fixes.

        0    Unknown: The fix coverage is unknown.
        1    Complete: All affected packages and components have available fixes or patches to
             remediate the vulnerability.
        2    Partial: Only some of the affected packages and components have available fixes or
             patches, while others remain vulnerable.
        3    None: No fixes or patches are currently available for any of the affected packages
             and components.
        99   Other: The fix coverage is not mapped. See the fix_coverage attribute, which contains
             a data source specific value.""",
    )
    is_exploit_available: Optional[bool] = Field(
        default=None,
        title="Exploit Availability",
        description="""Indicates if an exploit or a PoC (proof-of-concept) is available for the
        reported vulnerability.""",
    )
    is_fix_available: Optional[bool] = Field(
        default=None,
        title="Fix Availability",
        description="""Indicates if a fix is available for the reported vulnerability.""",
    )
    last_seen_time: Optional[int] = Field(
        default=None,
        title="Last Seen",
        description="""The time when the vulnerability was most recently observed.""",
    )
    last_seen_time_dt: Optional[datetime] = Field(
        default=None,
        title="Last Seen",
        description="""The time when the vulnerability was most recently observed.""",
    )
    references: Optional[List[str]] = Field(
        default=None,
        title="References",
        description="""A list of reference URLs with additional information about the vulnerability.""",
    )
    related_vulnerabilities: Optional[List[str]] = Field(
        default=None,
        title="Related Vulnerability IDs",
        description="""List of vulnerability IDs (e.g. CVE ID) that are related to this
        vulnerability.""",
    )
    remediation: Optional[Remediation] = Field(
        default=None,
        title="Remediation Guidance",
        description="""The remediation recommendations on how to mitigate the identified
        vulnerability.""",
    )
    severity: Optional[str] = Field(
        default=None,
        title="Severity",
        description="""The vendor assigned severity of the vulnerability.""",
    )
    title: Optional[str] = Field(
        default=None,
        title="Title",
        description="""A title or a brief phrase summarizing the discovered vulnerability.""",
    )
    vendor_name: Optional[str] = Field(
        default=None,
        title="Vendor Name",
        description="""The name of the vendor that identified the vulnerability.""",
    )

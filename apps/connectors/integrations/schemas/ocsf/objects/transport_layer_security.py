from typing import Any, List, Optional

from pydantic import BaseMode<PERSON>, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import TLSExtensionType

from .certificate import DigitalCertificate
from .file import Fingerprint


class TransportLayerSecurityExtension(BaseModel):
    """
    The TLS Extension object describes additional attributes that extend the base Transport Layer Security (TLS) object.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        TLSExtensionType.set_values(values, "type_id", "type")

        return values

    data: Optional[Any] = Field(
        default=None,
        title="Data",
        description="The data contains information specific to the particular extension type.",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="The type of the TLS extension.",
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="The integer value of the TLS extension type.",
    )


class TransportLayerSecurity(BaseModel):
    """
    The Transport Layer Security (TLS) object describes the negotiated TLS protocol
    used for secure communications over an established network connection.
    """

    alert: Optional[int] = Field(
        default=None,
        title="Client TLS Alert",
        description="The integer value of TLS alert if present. The alerts are defined in the TLS specification in RFC-2246.",
    )
    certificate: Optional[DigitalCertificate] = Field(
        default=None,
        title="Certificate",
        description="The certificate object containing information about the digital certificate.",
    )
    certificate_chain: Optional[List[str]] = Field(
        default=None,
        title="Certificate Chain",
        description="The Chain of Certificate Serial Numbers field provides a chain of Certificate Issuer Serial Numbers leading to the Root Certificate Issuer.",
    )
    cipher: Optional[str] = Field(
        default=None,
        title="Cipher Suite",
        description="The negotiated cipher suite.",
    )
    client_ciphers: Optional[List[str]] = Field(
        default=None,
        title="Client Cipher Suites",
        description="The client cipher suites that were exchanged during the TLS handshake negotiation.",
    )
    handshake_dur: Optional[int] = Field(
        default=None,
        title="Handshake Duration",
        description="The amount of total time for the TLS handshake to complete after the TCP connection is established, including client-side delays, in milliseconds.",
    )
    ja3_hash: Optional[Fingerprint] = Field(
        default=None,
        title="JA3 Hash",
        description="The MD5 hash of a JA3 string.",
    )
    ja3s_hash: Optional[Fingerprint] = Field(
        default=None,
        title="JA3S Hash",
        description="The MD5 hash of a JA3S string.",
    )
    key_length: Optional[int] = Field(
        default=None,
        title="Key Length",
        description="The length of the encryption key.",
    )
    server_ciphers: Optional[List[str]] = Field(
        default=None,
        title="Server Cipher Suites",
        description="The server cipher suites that were exchanged during the TLS handshake negotiation.",
    )
    sni: Optional[str] = Field(
        default=None,
        title="Server Name Indication",
        description="The Server Name Indication (SNI) extension sent by the client.",
    )
    tls_extension_list: Optional[List[TransportLayerSecurityExtension]] = Field(
        default=None,
        title="TLS Extension List",
        description="The list of TLS extensions.",
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="The TLS protocol version.",
    )

from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import (
    SoftwareComponentRelationship,
    SoftwareComponentType,
)

from .file import Fingerprint


class SoftwareComponent(BaseModel):
    """
    The Software Component object describes characteristics of a software component within
    a software package.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        SoftwareComponentRelationship.set_values(
            values, "relationship_id", "relationship"
        )
        SoftwareComponentType.set_values(values, "type_id", "type")
        return values

    author: Optional[str] = Field(
        default=None,
        title="Author",
        description="""
        The author(s) who published the software component.
        """,
    )
    hash: Optional[Fingerprint] = Field(
        default=None,
        title="Hash",
        description="""
        Cryptographic hash to identify the binary instance of a software component.
        """,
    )
    license: Optional[str] = Field(
        default=None,
        title="Software License",
        description="""
        The software license applied to this component.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The software component name.
        """,
    )
    purl: Optional[str] = Field(
        default=None,
        title="Package URL",
        description="""
        The Package URL (PURL) to identify the software component. This is a URL that
        uniquely identifies the component, including the component's name, version, and
        type. The URL is used to locate and retrieve the component's metadata and content.
        """,
    )
    related_component: Optional[str] = Field(
        default=None,
        title="Related Component",
        description="""
        The package URL (PURL) of the component that this software component has a
        relationship with.
        """,
    )
    relationship: Optional[str] = Field(
        default=None,
        title="Relationship",
        description="""
        The relationship between two software components, normalized to the caption of the
        `relationship_id` value. In the case of 'Other', it is defined by the source.
        """,
    )
    relationship_id: Optional[int] = Field(
        default=None,
        title="Relationship ID",
        description="""
        The normalized identifier of the relationship between two software components.

        0    Unknown: The relationship is unknown.
        1    Depends On: The component is a dependency of another component. Can be used to
             define both direct and transitive dependencies.
        99   Other: The relationship is not mapped. See the `relationship` attribute, which
             contains a data source specific value.
        """,
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""
        The type of software component, normalized to the caption of the `type_id` value. In
        the case of 'Other', it is defined by the source.
        """,
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The type of software component.

        0    Unknown: The type is unknown.
        1    Framework: A software framework.
        2    Library: A software library.
        3    Operating System: An operating system. Useful for SBOMs of container images.
        99   Other: The type is not mapped. See the `type` attribute, which contains a data
             source specific value.
        """,
    )
    version: Optional[str] = Field(
        default=None,
        title="Version",
        description="""
        The software component version.
        """,
    )

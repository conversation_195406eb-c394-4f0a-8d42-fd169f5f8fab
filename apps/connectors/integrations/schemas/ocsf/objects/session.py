from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class Session(BaseModel):
    """
    Represents an authenticated session.
    """

    count: Optional[int] = Field(
        default=None,
        title="Count",
        description="The number of identical sessions spawned from the same source IP, "
        "destination IP, application, and content/threat type seen over a period of time.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="The time the session was created.",
    )
    """
    Added by Date/Time profile
    """

    credential_uid: Optional[str] = Field(
        default=None,
        title="User Credential ID",
        description="The unique identifier of the credential used for authentication.",
    )
    expiration_reason: Optional[str] = Field(
        default=None,
        title="Expiration Reason",
        description="The reason which triggered the session expiration.",
    )
    expiration_time_dt: Optional[datetime] = Field(
        default=None,
        title="Expiration Time",
        description="The time the session expires.",
    )
    """
    Added by Date/Time profile
    """

    is_mfa: Optional[bool] = Field(
        default=None,
        title="Multi Factor Authentication",
        description="Indicates whether multi-factor authentication was used during authentication.",
    )
    is_remote: Optional[bool] = Field(
        default=None,
        title="Remote",
        description="Indicates whether the session is remote.",
    )
    is_vpn: Optional[bool] = Field(
        default=None,
        title="VPN Session",
        description="Indicates whether the session is a VPN session.",
    )
    issuer: Optional[str] = Field(
        default=None,
        title="Issuer Details",
        description="The identifier of the session issuer.",
    )
    terminal: Optional[str] = Field(
        default=None,
        title="Terminal",
        description="The Pseudo Terminal associated with the session. Ex: the tty or pts value.",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="The unique identifier of the session.",
    )
    uid_alt: Optional[str] = Field(
        default=None,
        title="Alternate ID",
        description="The alternate unique identifier of the session. "
        "e.g. AWS ARN - arn:aws:sts::123344444444:assumed-role/Admin/example-session.",
    )
    uuid: Optional[str] = Field(
        default=None,
        title="UUID",
        description="The universally unique identifier of the session.",
    )

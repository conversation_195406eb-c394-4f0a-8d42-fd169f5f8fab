from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from apps.connectors.integrations.schemas.ocsf.objects import Email

from .autonomous_system import AutonomousSystem


class WhoisInfo(BaseModel):
    """https://schema.ocsf.io/1.4.0/objects/whois?extensions="""

    autonomous_system: Optional[AutonomousSystem] = Field(
        default=None,
        description="The autonomous system information associated with a domain.",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        description="When the domain was registered or WHOIS entry was created.",
    )
    """
    Added by Date/Time profile
    """

    dnssec_status: Optional[str] = Field(
        default=None,
        description="""
        The normalized value of dnssec_status_id.
        This is the string sibling of enum attribute dnssec_status_id.
        """,
    )
    dnssec_status_id: Optional[int] = Field(
        default=None,
        description="""
        Describes the normalized status of DNS Security Extensions (DNSSEC) for a domain.
            0	Unknown
                The disposition is unknown.
            1	Signed
                The related domain enables the signing of DNS records using DNSSEC.
            2	Unsigned
                The related domain does not enable the signing of DNS records using DNSSEC.
            99	Other
                The DNSSEC status is not mapped. See the dnssec_status attribute, which contains a data source specific value.
        This is an enum attribute; its string sibling is dnssec_status.""",
    )
    domain: Optional[str] = Field(
        default=None,
        description="The domain name corresponding to the WHOIS record.",
    )
    email_addr: Optional[Email] = Field(
        default=None,
        description="The email address for the registrar's abuse contact",
    )
    last_seen_time_dt: Optional[datetime] = Field(
        default=None,
        description="When the WHOIS record was last updated or seen at.",
    )
    """
    Added by Date/Time profile
    """

    name_servers: Optional[List[str]] = Field(
        default=None,
        description="A collection of name servers related to a domain registration or other record.",
    )
    registrar: Optional[str] = Field(
        default=None,
        description="The domain registrar.",
    )
    status: Optional[str] = Field(
        default=None,
        description="The status of a domain and its ability to be transferred, e.g., clientTransferProhibited.",
    )

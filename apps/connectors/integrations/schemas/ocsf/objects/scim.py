from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import AuthProtocol, SCIMState


class SCIM(BaseModel):
    """
    The System for Cross-domain Identity Management (SCIM) Configuration object provides a
    structured set of attributes related to SCIM protocols used for identity provisioning and
    management across cloud-based platforms.
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        AuthProtocol.set_values(values, "auth_protocol_id", "auth_protocol")
        SCIMState.set_values(values, "state_id", "state")
        return values

    auth_protocol: Optional[str] = Field(
        default=None,
        title="Auth Protocol",
        description="""
        The authorization protocol as defined by the caption of `auth_protocol_id`. In the case of
        `Other`, it is defined by the event source.
        """,
    )
    auth_protocol_id: Optional[int] = Field(
        default=None,
        title="Auth Protocol ID",
        description="""
        The normalized identifier of the authorization protocol used by the SCIM resource.
        0    Unknown: The authentication protocol is unknown.
        1    NTLM
        2    Kerberos
        3    Digest
        4    OpenID
        5    SAML
        6    OAUTH 2.0
        7    PAP
        8    CHAP
        9    EAP
        10   RADIUS
        11   Basic Authentication
        12   LDAP
        99   Other: See `auth_protocol` attribute for source-specific value.
        """,
    )
    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        When the SCIM resource was added to the service provider.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        When the SCIM resource was added to the service provider.
        """,
    )
    error_message: Optional[str] = Field(
        default=None,
        title="Last Error Message",
        description="""
        Message or code associated with the last encountered error.
        """,
    )
    is_group_provisioning_enabled: Optional[bool] = Field(
        default=None,
        title="SCIM Group Provisioning Enabled",
        description="""
        Indicates whether the SCIM resource is configured to provision groups, automatically or
        otherwise.
        """,
    )
    is_user_provisioning_enabled: Optional[bool] = Field(
        default=None,
        title="SCIM User Provisioning Enabled",
        description="""
        Indicates whether the SCIM resource is configured to provision users, automatically or
        otherwise.
        """,
    )
    last_run_time: Optional[int] = Field(
        default=None,
        title="Last Sync Time",
        description="""
        Timestamp of the most recent successful synchronization.
        """,
    )
    last_run_time_dt: Optional[datetime] = Field(
        default=None,
        title="Last Sync Time",
        description="""
        Timestamp of the most recent successful synchronization.
        """,
    )
    modified_time: Optional[int] = Field(
        default=None,
        title="Modified Time",
        description="""
        The most recent time when the SCIM resource was updated at the service provider.
        """,
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="""
        The most recent time when the SCIM resource was updated at the service provider.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The name of the SCIM resource.
        """,
    )
    protocol_name: Optional[str] = Field(
        default=None,
        title="Supported Protocol",
        description="""
        The supported protocol for the SCIM resource. E.g., `SAML`, `OIDC`, or `OAuth2`.
        """,
    )
    rate_limit: Optional[int] = Field(
        default=None,
        title="Rate Limit",
        description="""
        Maximum number of requests allowed by the SCIM resource within a specified time frame to
        avoid throttling.
        """,
    )
    scim_group_schema: Optional[str] = Field(
        default=None,
        title="SCIM Group Schema",
        description="""
        SCIM provides a schema for representing groups, identified using the following schema URI:
        `urn:ietf:params:scim:schemas:core:2.0:Group`. This attribute captures key-value pairs for
        the scheme implemented in a SCIM resource.
        """,
    )
    scim_user_schema: Optional[str] = Field(
        default=None,
        title="SCIM User Schema",
        description="""
        SCIM provides a resource type for user resources, including the Enterprise User Schema
        Extension. This attribute captures key-value pairs for the scheme implemented in a SCIM
        resource.
        """,
    )
    state: Optional[str] = Field(
        default=None,
        title="State",
        description="""
        The provisioning state of the SCIM resource, normalized to the caption of the `state_id`
        value. In the case of `Other`, it is defined by the event source.
        """,
    )
    state_id: Optional[int] = Field(
        default=None,
        title="State ID",
        description="""
        The normalized state ID of the SCIM resource to reflect its activation status.
        0    Unknown: The provisioning state of the SCIM resource is unknown.
        1    Pending: The SCIM resource is Pending activation or creation.
        2    Active: The SCIM resource is in an Active state, or otherwise enabled.
        3    Failed: The SCIM resource is in a Failed state.
        4    Deleted: The SCIM resource is in a Deleted state, or otherwise disabled.
        99   Other: See `state` attribute for source-specific value.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        A unique identifier for a SCIM resource as defined by the service provider.
        """,
    )
    uid_alt: Optional[str] = Field(
        default=None,
        title="External ID",
        description="""
        A String that is an identifier for the resource as defined by the provisioning client. It
        allows the client to locate the resource using a filter without needing to store a mapping
        between the client and provider identifiers.
        """,
    )
    url_string: Optional[str] = Field(
        default=None,
        title="SCIM Endpoint URL",
        description="""
        The primary URL for SCIM API requests.
        """,
    )
    vendor_name: Optional[str] = Field(
        default=None,
        title="Service Provider",
        description="""
        Name of the vendor or service provider implementing SCIM. E.g., `Okta`, `Auth0`,
        `Microsoft`.
        """,
    )
    version: Optional[str] = Field(
        default=None,
        title="SCIM Version",
        description="""
        SCIM protocol version supported e.g., `SCIM 2.0`.
        """,
    )

from __future__ import annotations

from typing import TYPE_CHECKING, List, Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import RiskLevel, UserType

from .account import Account
from .group import Group
from .organization import Organization

if TYPE_CHECKING:
    from .ldap_person import LDA<PERSON>erson


class User(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        RiskLevel.set_values(values, "risk_level_id", "risk_level")
        UserType.set_values(values, "type_id", "type")

        return values

    account: Optional[Account] = Field(
        default=None,
        title="Account",
        description="The user's account or the account associated with the user.",
    )
    credential_uid: Optional[str] = Field(
        default=None,
        title="User Credential ID",
        description="The unique identifier of the user's credential. For example, AWS Access Key ID.",
    )
    display_name: Optional[str] = Field(
        default=None,
        title="Display Name",
        description="The display name of the user, as reported by the product.",
    )
    domain: Optional[str] = Field(
        default=None,
        title="Domain",
        description="The domain where the user is defined. For example: the LDAP or Active Directory domain.",
    )
    email_addr: Optional[str] = Field(
        default=None,
        title="Email Address",
        description="The user's primary email address.",
    )
    forward_addr: Optional[str] = Field(
        default=None,
        title="Forwarding Address",
        description="The user's forwarding email address.",
    )
    full_name: Optional[str] = Field(
        default=None,
        title="Full Name",
        description="The full name of the user, as reported by the product.",
    )
    groups: Optional[List[Group]] = Field(
        default=None,
        title="Groups",
        description="The administrative groups to which the user belongs.",
    )
    has_mfa: Optional[bool] = Field(
        default=None,
        title="MFA Assigned",
        description="The user has a multi-factor or secondary-factor device assigned.",
    )
    is_enabled: Optional[bool] = Field(
        default=None,
        description="Indicates if the user is enabled. (create OCSF schema PR to add this field)",
    )
    ldap_person: Optional[LDAPPerson] = Field(
        default=None,
        title="LDAP Person",
        description="The additional LDAP attributes that describe a person.",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="The username. For example, janedoe1.",
    )
    org: Optional[Organization] = Field(
        default=None,
        title="Organization",
        description="Organization and org unit related to the user.",
    )
    phone_number: Optional[str] = Field(
        default=None,
        title="Telephone Number",
        description="The telephone number of the user.",
    )
    risk_level: Optional[str] = Field(
        default=None,
        title="Risk Level",
        description="The risk level, normalized to the caption of the risk_level_id value.",
    )
    risk_level_id: Optional[int] = Field(
        default=None,
        title="Risk Level ID",
        description="""
        The normalized risk level id.
            0	Info
            1	Low
            2	Medium
            3	High
            4	Critical
            99	Other
                The risk level is not mapped. See the risk_level attribute, which
                contains a data source specific value.
        """,
    )
    risk_score: Optional[int] = Field(
        default=None,
        title="Risk Score",
        description="The risk score as reported by the event source.",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="The type of the user. For example, System, AWS IAM User, etc.",
    )
    type_id: Optional[int] = Field(
        default=None,
        title="Type ID",
        description="""
        The account type identifier.
        0	Unknown
            The type is unknown.
        1	User
            Regular user account.
        2	Admin
            Admin/root user account.
        3	System
            System account. For example, Windows computer accounts with a trailing dollar sign ($).
        99	Other
            The type is not mapped. See the type attribute, which contains a data source specific value.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="The unique user identifier. For example, the Windows user SID, "
        "ActiveDirectory DN or AWS user ARN.",
    )
    uid_alt: Optional[str] = Field(
        default=None,
        title="Alternate ID",
        description="The alternate user identifier. For example, the Active Directory "
        "user GUID or AWS user Principal ID.",
    )
    """
    The platform uses `uid_alt` for UPN.
    """

from typing import Optional
from urllib.parse import urlparse

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import UrlCategory


class Url(BaseModel):
    """
    Represents a URL using OCSF format: https://schema.ocsf.io/1.4.0/objects/url
    """

    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        UrlCategory.set_values(values, "category_ids", "categories")
        return values

    @model_validator(mode="before")
    def _set_extract_fields_from_url_string(cls, values):
        url_string = values.get("url_string")
        if not url_string:
            return values

        parsed_url = urlparse(url_string)

        # Map of attributes to extract from parsed_url
        url_attributes = [
            "scheme",
            "netloc",
            "hostname",
            "port",
            "path",
            "params",
            "query",
            "fragment",
        ]

        # Only set values for attributes that are not empty
        for attr in url_attributes:
            value = getattr(parsed_url, attr, None)
            if value:
                values[attr] = value

        return values

    domain: Optional[str] = Field(
        default=None,
        title="Domain",
        description="Domain portion of the URL.",
    )
    resource_type: Optional[str] = Field(
        default=None,
        title="Resource Type",
        description="Resource type of the URL.The context in which a resource was retrieved in a web request.",
    )
    url_string: Optional[str] = Field(
        default=None,
        title="URL String",
        description="The URL string.",
    )
    category_ids: Optional[list[int]] = Field(
        default=None,
        title="Category IDs",
        description="List of category IDs associated with the URL.",
    )
    categories: Optional[list[str]] = Field(
        default=None,
        title="Categories",
        description="List of categories associated with the URL.",
    )
    scheme: Optional[str] = Field(
        default=None,
        title="Scheme",
        description="Scheme portion of the URL.",
    )
    port: Optional[int] = Field(
        default=None,
        title="Port",
        description="Port portion of the URL.",
    )
    netloc: Optional[str] = Field(
        default=None,
        title="Netloc",
        description="Netloc portion of the URL.",
    )
    hostname: Optional[str] = Field(
        default=None,
        title="Hostname",
        description="Hostname portion of the URL.",
    )
    path: Optional[str] = Field(
        default=None,
        title="Path",
        description="Path portion of the URL.",
    )
    params: Optional[str] = Field(
        default=None,
        title="Params",
        description="Params portion of the URL.",
    )
    query: Optional[str] = Field(
        default=None,
        title="Query",
        description="Query portion of the URL.",
    )
    fragment: Optional[str] = Field(
        default=None,
        title="Fragment",
        description="Fragment portion of the URL.",
    )

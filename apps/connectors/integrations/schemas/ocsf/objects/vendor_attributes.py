from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import Severity


class VendorAttributes(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        Severity.set_values(values, "severity_id", "severity")

        return values

    severity: Optional[str] = Field(
        default=None,
        description="""
        The severity of the finding.
        """,
    )
    severity_id: Optional[int] = Field(
        default=None,
        description="""
        The severity identifier of the finding.
        """,
    )

from typing import Optional

from pydantic import BaseModel, Field, model_validator

from apps.connectors.integrations.schemas.ocsf.enums import TimeSpanType


class TimeSpan(BaseModel):
    @model_validator(mode="before")
    def _set_enum_fields(cls, values):
        TimeSpanType.set_values(values, "type_id", "type")

        return values

    duration: Optional[int] = Field(
        default=None,
        description="The duration of the time span in milliseconds.",
    )
    duration_days: Optional[int] = Field(
        default=None,
        description="The duration of the time span in days.",
    )
    duration_hours: Optional[int] = Field(
        default=None,
        description="The duration of the time span in hours.",
    )
    duration_mins: Optional[int] = Field(
        default=None,
        description="The duration of the time span in minutes.",
    )
    duration_months: Optional[int] = Field(
        default=None,
        description="The duration of the time span in months.",
    )
    duration_secs: Optional[int] = Field(
        default=None,
        description="The duration of the time span in seconds.",
    )
    duration_weeks: Optional[int] = Field(
        default=None,
        description="The duration of the time span in weeks.",
    )
    duration_years: Optional[int] = Field(
        default=None,
        description="The duration of the time span in years.",
    )
    type: Optional[str] = Field(
        default=None,
        description="The type of time span duration the object represents.",
    )
    type_id: Optional[int] = Field(
        default=None,
        description="The normalized identifier for the time span duration type. "
        "Possible values: 0 (Unknown), 1 (Milliseconds), 2 (Seconds), 3 (Minutes), "
        "4 (Hours), 5 (Days), 6 (Weeks), 7 (Months), 8 (Years), 99 (Other).",
    )

from datetime import datetime
from typing import Any, List, Optional

from pydantic import BaseModel, Field

from .data_classification import DataClassification
from .key_value_object import KeyValueObject


class WebResource(BaseModel):
    """
    The Web Resource object describes characteristics of a web resource that was
    affected by the activity/event.
    """

    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""The time when the resource was created.""",
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""The time when the resource was created.""",
    )
    data: Optional[Any] = Field(
        default=None,
        title="Data",
        description="""Details of the web resource, e.g, file details, search results or
        application-defined resource.""",
    )
    data_classifications: Optional[List[DataClassification]] = Field(
        default=None,
        title="Data Classification",
        description="""A list of Data Classification objects, that include information about data
        classification levels and data category types, indentified by a classifier.""",
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""Description of the web resource.""",
    )
    labels: Optional[List[str]] = Field(
        default=None,
        title="Labels",
        description="""The list of labels associated to the resource.""",
    )
    modified_time: Optional[int] = Field(
        default=None,
        title="Modified Time",
        description="""The time when the resource was last modified.""",
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="""The time when the resource was last modified.""",
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""The name of the web resource.""",
    )
    tags: Optional[List[KeyValueObject]] = Field(
        default=None,
        title="Tags",
        description="""The list of tags; {key:value} pairs associated to the resource.""",
    )
    type: Optional[str] = Field(
        default=None,
        title="Type",
        description="""The web resource type as defined by the event source.""",
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""The unique identifier of the web resource.""",
    )
    uid_alt: Optional[str] = Field(
        default=None,
        title="Alternate ID",
        description="""The alternative unique identifier of the resource.""",
    )
    url_string: Optional[str] = Field(
        default=None,
        title="URL String",
        description="""The URL pointing towards the source of the web resource.""",
    )

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field

from .group import Group


class Table(BaseModel):
    """
    The table object represents a table within a structured relational database
    or datastore, which contains columns and rows of data that are able to be
    create, updated, deleted and queried.
    """

    created_time: Optional[int] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the table was known to have been created.
        """,
    )
    created_time_dt: Optional[datetime] = Field(
        default=None,
        title="Created Time",
        description="""
        The time when the table was known to have been created.
        """,
    )
    desc: Optional[str] = Field(
        default=None,
        title="Description",
        description="""
        The description of the table.
        """,
    )
    groups: Optional[List[Group]] = Field(
        default=None,
        title="Groups",
        description="""
        The group names to which the table belongs.
        """,
    )
    modified_time: Optional[int] = Field(
        default=None,
        title="Modified Time",
        description="""
        The most recent time when any changes, updates, or modifications were made
        within the table.
        """,
    )
    modified_time_dt: Optional[datetime] = Field(
        default=None,
        title="Modified Time",
        description="""
        The most recent time when any changes, updates, or modifications were made
        within the table.
        """,
    )
    name: Optional[str] = Field(
        default=None,
        title="Name",
        description="""
        The table name, ordinarily as assigned by a database administrator.
        """,
    )
    size: Optional[int] = Field(
        default=None,
        title="Size",
        description="""
        The size of the data table in bytes.
        """,
    )
    uid: Optional[str] = Field(
        default=None,
        title="Unique ID",
        description="""
        The unique identifier of the table.
        """,
    )

from .ocsf_enum import OCSFEnum


class DetectionActivity(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    CREATE = (1, "Create")
    UPDATE = (2, "Update")
    CLOSE = (3, "Close")
    OTHER = (99, "Other")


class IncidentActivity(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    CREATE = (1, "Create")
    UPDATE = (2, "Update")
    CLOSE = (3, "Close")
    OTHER = (99, "Other")


class NetworkActivityType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    OPEN = (1, "Open")
    CLOSE = (2, "Close")
    RESET = (3, "Reset")
    FAIL = (4, "Fail")
    REFUSE = (5, "Refuse")
    TRAFFIC = (6, "Traffic")
    LISTEN = (7, "Listen")
    OTHER = (99, "Other")


class AuthenticationActivity(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    LOGON = (1, "Logon")
    LOGOFF = (2, "Logoff")
    AUTHENTICATION_TICKET = (3, "Authentication Ticket")
    SERVICE_TICKET_REQUEST = (4, "Service Ticket Request")
    SERVICE_TICKET_RENEW = (5, "Service Ticket Renew")
    PREAUTH = (6, "Preauth")
    OTHER = (99, "Other")


class EmailActivityType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    SEND = (1, "Send")
    RECEIVE = (2, "Receive")
    SCAN = (3, "Scan")
    TRACE = (4, "Trace")
    OTHER = (99, "Other")

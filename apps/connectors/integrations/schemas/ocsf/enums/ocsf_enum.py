from enum import Enum


class OCSFEnum(Enum):
    def __init__(self, id: int, name: str):
        self._id = id
        self._name = name

    @property
    def id(self) -> int:
        return self._id

    @property
    def name(self) -> str:
        return self._name

    def __str__(self):
        return f"{self.id} - {self.name}"

    @classmethod
    def from_id(cls, id: int):
        return next((member for member in cls if member.id == id), None)

    @classmethod
    def set_values(cls, values, id_key, name_key, enum_key=None):
        """
        Sets the values for the given keys in the provided dictionary.

        Args:
            cls (OCSFEnum): The enum class.
            values (dict): The dictionary to update.
            id_key (str): The key for the ID value (or list of ID values).
            name_key (str): The key for the name value (or list of name values).
            enum_key (str, optional): The key for the enum value. Defaults to name_key.

        If the value corresponding to enum_key is an instance of the enum class,
        it sets the id_key and name_key in the dictionary to the enum's id and name.
        If the value is not an instance of the enum class and the enum class has an "OTHER" attribute,
        it sets the id_key to the id of the "OTHER" enum and the name_key to the value.
        """
        if enum_key is None:
            enum_key = name_key
        value = values.get(enum_key)

        def get_id_and_name(value):
            if isinstance(value, cls):
                return value.id, value.name
            if value and hasattr(cls, "OTHER"):
                # When the value is a str defined by the source,
                # set the enum to OTHER and set the name to the value
                return cls.OTHER.id, value  # type: ignore

            return None, None

        if isinstance(value, list):
            value_ids = []
            value_names = []
            for v in value:
                id_, name = get_id_and_name(v)
                value_ids.append(id_)
                value_names.append(name)
            values[id_key] = value_ids
            values[name_key] = value_names
        else:
            value_id, value_name = get_id_and_name(value)
            if value_id is not None and value_name is not None:
                values[id_key] = value_id
                values[name_key] = value_name

from .ocsf_enum import OCSFEnum


class Category(OCSFEnum):
    SYSTEM_ACTIVITY = (1, "System Activity")
    FINDINGS = (2, "Findings")
    IDENTITY_AND_ACCESS_MANAGEMENT = (3, "Identity & Access Management")
    NETWORK_ACTIVITY = (4, "Network Activity")
    DISCOVERY = (5, "Discovery")
    APPLICATION_ACTIVITY = (6, "Application Activity")
    REMEDIATION = (7, "Remediation")
    UNMANNED_SYSTEMS = (8, "Unmanned Systems")


class SystemActivity(OCSFEnum):
    FILE_SYSTEM_ACTIVITY = (1001, "File System Activity")
    KERNEL_EXTENSION_ACTIVITY = (1002, "Kernel Extension Activity")
    KERNEL_ACTIVITY = (1003, "Kernel Activity")
    MEMORY_ACTIVITY = (1004, "Memory Activity")
    MODULE_ACTIVITY = (1005, "Module Activity")
    SCHEDULED_JOB_ACTIVITY = (1006, "Scheduled Job Activity")
    PROCESS_ACTIVITY = (1007, "Process Activity")
    EVENT_LOG_ACTIVITY = (1008, "Event Log Activity")
    SCRIPT_ACTIVITY = (1009, "Script Activity")


class Findings(OCSFEnum):
    VULNERABILITY_FINDING = (2002, "Vulnerability Finding")
    COMPLIANCE_FINDING = (2003, "Compliance Finding")
    DETECTION_FINDING = (2004, "Detection Finding")
    INCIDENT_FINDING = (2005, "Incident Finding")
    DATA_SECURITY_FINDING = (2006, "Data Security Finding")


class IdentityAndAccessManagement(OCSFEnum):
    ACCOUNT_CHANGE = (3001, "Account Change")
    AUTHENTICATION = (3002, "Authentication")
    AUTHORIZE_SESSION = (3003, "Authorize Session")
    ENTITY_MANAGEMENT = (3004, "Entity Management")
    USER_ACCESS_MANAGEMENT = (3005, "User Access Management")
    GROUP_MANAGEMENT = (3006, "Group Management")


class NetworkActivityClass(OCSFEnum):
    NETWORK_ACTIVITY = (4001, "Network Activity")
    HTTP_ACTIVITY = (4002, "HTTP Activity")
    DNS_ACTIVITY = (4003, "DNS Activity")
    DHCP_ACTIVITY = (4004, "DHCP Activity")
    RDP_ACTIVITY = (4005, "RDP Activity")
    SMB_ACTIVITY = (4006, "SMB Activity")
    SSH_ACTIVITY = (4007, "SSH Activity")
    FTP_ACTIVITY = (4008, "FTP Activity")
    EMAIL_ACTIVITY = (4009, "Email Activity")
    NTP_ACTIVITY = (4013, "NTP Activity")
    TUNNEL_ACTIVITY = (4014, "Tunnel Activity")


class Discovery(OCSFEnum):
    INVENTORY_INFO = (5001, "Device Inventory Info")
    CONFIG_STATE = (5002, "Device Config State")
    USER_INVENTORY = (5003, "User Inventory Info")
    PATCH_STATE = (5004, "Operating System Patch State")
    KERNEL_OBJECT_QUERY = (5006, "Kernel Object Query")
    FILE_QUERY = (5007, "File Query")
    FOLDER_QUERY = (5008, "Folder Query")
    ADMIN_GROUP_QUERY = (5009, "Admin Group Query")
    JOB_QUERY = (5010, "Job Query")
    MODULE_QUERY = (5011, "Module Query")
    NETWORK_CONNECTION_QUERY = (5012, "Network Connection Query")
    NETWORKS_QUERY = (5013, "Networks Query")
    PERIPHERAL_DEVICE_QUERY = (5014, "Peripheral Device Query")
    PROCESS_QUERY = (5015, "Process Query")
    SERVICE_QUERY = (5016, "Service Query")
    SESSION_QUERY = (5017, "User Session Query")
    USER_QUERY = (5018, "User Query")
    DEVICE_CONFIG_STATE_CHANGE = (5019, "Device Config State Change")
    SOFTWARE_INFO = (5020, "Software Inventory Info")
    OSINT_INVENTORY_INFO = (5021, "OSINT Inventory Info")
    STARTUP_ITEM_QUERY = (5022, "Startup Item Query")
    CLOUD_RESOURCES_INVENTORY_INFO = (5023, "Cloud Resources Inventory Info")


class ApplicationActivity(OCSFEnum):
    WEB_RESOURCES_ACTIVITY = (6001, "Web Resources Activity")
    APPLICATION_LIFECYCLE = (6002, "Application Lifecycle")
    API_ACTIVITY = (6003, "API Activity")
    DATASTORE_ACTIVITY = (6005, "Datastore Activity")
    FILE_HOSTING = (6006, "File Hosting Activity")
    SCAN_ACTIVITY = (6007, "Scan Activity")
    APPLICATION_ERROR = (6008, "Application Error")


class Remediation(OCSFEnum):
    REMEDIATION_ACTIVITY = (7001, "Remediation Activity")
    FILE_REMEDIATION_ACTIVITY = (7002, "File Remediation Activity")
    NETWORK_REMEDIATION_ACTIVITY = (7004, "Network Remediation Activity")
    PROCESS_REMEDIATION_ACTIVITY = (7003, "Process Remediation Activity")


class UnmannedSystems(OCSFEnum):
    DRONE_FLIGHTS_ACTIVITY = (8001, "Drone Flights Activity")
    AIRBORNE_BROADCAST_ACTIVITY = (8002, "Airborne Broadcast Activity")


class ClassificationCategory(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    PERSONAL = (1, "Personal")
    GOVERNMENTAL = (2, "Governmental")
    FINANCIAL = (3, "Financial")
    BUSINESS = (4, "Business")
    MILITARY_AND_LAW_ENFORCEMENT = (5, "Military/Law Enforcement")
    SECURITY = (6, "Security")
    OTHER = (99, "Other")


class ServiceCategory(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    KERNEL_MODE = (1, "Kernel Mode")
    USER_MODE = (2, "User Mode")
    OTHER = (99, "Other")

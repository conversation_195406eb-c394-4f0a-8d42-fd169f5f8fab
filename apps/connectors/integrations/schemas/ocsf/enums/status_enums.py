from .ocsf_enum import OCSFEnum


class EventStatus(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    SUCCESS = (1, "Success")
    FAILURE = (2, "Failure")
    OTHER = (99, "Other")


class IncidentStatus(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    NEW = (1, "New")
    IN_PROGRESS = (2, "In Progress")
    ON_HOLD = (3, "On Hold")
    RESOLVED = (4, "Resolved")
    CLOSED = (5, "Closed")
    OTHER = (99, "Other")


class DetectionStatus(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    NEW = (1, "New")
    IN_PROGRESS = (2, "In Progress")
    SUPPRESSED = (3, "Suppressed")
    RESOLVED = (4, "Resolved")
    ARCHIVED = (5, "Archived")
    OTHER = (99, "Other")


class ClassificationStatus(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    PENDING = (1, "Pending")
    COMPLETED = (2, "Completed")
    FAILED = (3, "Failed")
    SKIPPED = (4, "Skipped")
    OTHER = (99, "Other")

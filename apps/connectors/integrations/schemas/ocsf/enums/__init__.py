from enum import StrEnum

from .activity_enums import *
from .category_enums import *
from .ocsf_enum import OCSFEnum
from .status_enums import *


class Type(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    OTHER = (99, "Other")


class Confidence(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    LOW = (1, "Low")
    MEDIUM = (2, "Medium")
    HIGH = (3, "High")
    OTHER = (99, "Other")


class Impact(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    LOW = (1, "Low")
    MEDIUM = (2, "Medium")
    HIGH = (3, "High")
    CRITICAL = (4, "Critical")
    OTHER = (99, "Other")


class Priority(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    LOW = (1, "Low")
    MEDIUM = (2, "Medium")
    HIGH = (3, "High")
    CRITICAL = (4, "Critical")
    OTHER = (99, "Other")


class ControlAction(OCSFEnum):
    """
    The action taken by a control or other policy-based system leading to an
    outcome or disposition. An unknown action may still correspond to a known
    disposition. Refer to disposition_id for the outcome of the action.
    """

    UNKNOWN = (0, "Unknown")
    ALLOWED = (1, "Allowed")
    DENIED = (2, "Denied")
    OBSERVED = (3, "Observed")
    MODIFIED = (4, "Modified")
    OTHER = (99, "Other")


class Disposition(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    ALLOWED = (1, "Allowed")
    BLOCKED = (2, "Blocked")
    QUARANTINED = (3, "Quarantined")
    ISOLATED = (4, "Isolated")
    DELETED = (5, "Deleted")
    DROPPED = (6, "Dropped")
    CUSTOM_ACTION = (7, "Custom Action")
    APPROVED = (8, "Approved")
    RESTORED = (9, "Restored")
    EXONERATED = (10, "Exonerated")
    CORRECTED = (11, "Corrected")
    PARTIALLY_CORRECTED = (12, "Partially Corrected")
    UNCORRECTED = (13, "Uncorrected")
    DELAYED = (14, "Delayed")
    DETECTED = (15, "Detected")
    NO_ACTION = (16, "No Action")
    LOGGED = (17, "Logged")
    TAGGED = (18, "Tagged")
    ALERT = (19, "Alert")
    COUNT = (20, "Count")
    RESET = (21, "Reset")
    CAPTCHA = (22, "Captcha")
    CHALLENGE = (23, "Challenge")
    ACCESS_REVOKED = (24, "Access Revoked")
    REJECTED = (25, "Rejected")
    UNAUTHORIZED = (26, "Unauthorized")
    ERROR = (27, "Error")
    OTHER = (99, "Other")


class Verdict(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    FALSE_POSITIVE = (1, "False Positive")
    TRUE_POSITIVE = (2, "True Positive")
    DISREGARD = (3, "Disregard")
    SUSPICIOUS = (4, "Suspicious")
    BENIGN = (5, "Benign")
    TEST = (6, "Test")
    INSUFFICIENT_DATA = (7, "Insufficient Data")
    SECURITY_RISK = (8, "Security Risk")
    MANAGED_EXTERNALLY = (9, "Managed Externally")
    DUPLICATE = (10, "Duplicate")
    OTHER = (99, "Other")


class AccountType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    LDAP_ACCOUNT = (1, "LDAP Account")
    WINDOWS_ACCOUNT = (2, "Windows Account")
    AWS_IAM_USER = (3, "AWS IAM User")
    AWS_IAM_ROLE = (4, "AWS IAM Role")
    GCP_ACCOUNT = (5, "GCP Account")
    AZURE_AD_ACCOUNT = (6, "Azure AD Account")
    MAC_OS_ACCOUNT = (7, "Mac OS Account")
    APPLE_ACCOUNT = (8, "Apple Account")
    LINUX_ACCOUNT = (9, "Linux Account")
    AWS_ACCOUNT = (10, "AWS Account")
    GCP_PROJECT = (11, "GCP Project")
    OCI_COMPARTMENT = (12, "OCI Compartment")
    AZURE_SUBSCRIPTION = (13, "Azure Subscription")
    SALESFORCE_ACCOUNT = (14, "Salesforce Account")
    GOOGLE_WORKSPACE = (15, "Google Workspace")
    SERVICENOW_INSTANCE = (16, "Servicenow Instance")
    M365_TENANT = (17, "M365 Tenant")
    EMAIL_ACCOUNT = (18, "Email Account")
    OTHER = (99, "Other")


class UserType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    USER = (1, "User")
    ADMIN = (2, "Admin")
    SYSTEM = (3, "System")
    OTHER = (99, "Other")


class AnalyticType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    RULE = (1, "Rule")
    BEHAVIORAL = (2, "Behavioral")
    STATISTICAL = (3, "Statistical")
    LEARNING = (4, "Learning")
    FINGERPRINTING = (5, "Fingerprinting")
    TAGGING = (6, "tagging")
    KEYWORD_MATCH = (7, "keyword Match")
    REGEX = (8, "Regex")
    EXACT_DATA_MATCH = (9, "Exact Data Match")
    PARTIAL_DATA_MATCH = (10, "Partial Data Match")
    INDEXED_DATA_MATCH = (11, "Index Data Match")
    OTHER = (99, "Other")


class KillChainPhaseEnum(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    RECONNAISSANCE = (1, "Reconnaissance")
    WEAPONIZATION = (2, "Weaponization")
    DELIVERY = (3, "Delivery")
    EXPLOITATION = (4, "Exploitation")
    INSTALLATION = (5, "Installation")
    COMMAND_AND_CONTROL = (6, "Command And Control")
    ACTIONS_ON_OBJECTIVES = (7, "Actions On Objectives")
    OTHER = (99, "Other")


class Severity(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    INFORMATIONAL = (1, "Informational")
    LOW = (2, "Low")
    MEDIUM = (3, "Medium")
    HIGH = (4, "High")
    CRITICAL = (5, "Critical")
    FATAL = (6, "Fatal")
    OTHER = (99, "Other")


class ObservableType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    HOSTNAME = (1, "Hostname")
    IP_ADDRESS = (2, "IP Address")
    MAC_ADDRESS = (3, "MAC Address")
    USER_NAME = (4, "User Name")
    EMAIL_ADDRESS = (5, "Email Address")
    URL_STRING = (6, "URL String")
    FILE_NAME = (7, "File Name")
    HASH = (8, "Hash")
    PROCESS_NAME = (9, "Process Name")
    RESOURCE_UID = (10, "Resource UID")
    PORT = (11, "Port")
    SUBNET = (12, "Subnet")
    COMMAND_LINE = (13, "Command Line")
    COUNTRY = (14, "Country")
    PROCESS_ID = (15, "Process ID")
    HTTP_USER_AGENT = (16, "HTTP User-Agent")
    CWE_UID = (17, "CWE Object: uid")
    CVE_UID = (18, "CVE Object: uid")
    USER_CREDENTIAL_ID = (19, "User Credential ID")
    ENDPOINT = (20, "Endpoint")
    USER = (21, "User")
    EMAIL = (22, "Email")
    URL_OBJECT = (23, "Uniform Resource Locator")
    FILE = (24, "File")
    PROCESS = (25, "Process")
    GEO_LOCATION = (26, "Geo Location")
    CONTAINER = (27, "Container")
    REGISTRY_KEY = (28, "Registry Key")
    REGISTRY_VALUE = (29, "Registry Value")
    FINGERPRINT = (30, "Fingerprint")
    USER_OBJECT_UID = (31, "User Object: uid")
    GROUP_OBJECT_NAME = (32, "Group Object: name")
    GROUP_OBJECT_UID = (33, "Group Object: uid")
    ACCOUNT_OBJECT_NAME = (34, "Account Object: name")
    ACCOUNT_OBJECT_UID = (35, "Account Object: uid")
    SCRIPT_CONTENT = (36, "Script Content")
    SERIAL_NUMBER = (37, "Serial Number")
    RESOURCE_DETAILS_OBJECT_NAME = (38, "Resource Details Object: name")
    OTHER = (99, "Other")


class TimeSpanType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    MILLISECONDS = (1, "Milliseconds")
    SECONDS = (2, "Seconds")
    MINUTES = (3, "Minutes")
    HOURS = (4, "Hours")
    DAYS = (5, "Days")
    WEEKS = (6, "Weeks")
    MONTHS = (7, "Months")
    YEARS = (8, "Years")
    OTHER = (99, "Other")


class OSType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    OTHER = (99, "Other")
    WINDOWS = (100, "Windows")
    WINDOWS_MOBILE = (101, "Windows Mobile")
    LINUX = (200, "Linux")
    ANDROID = (201, "Android")
    MACOS = (300, "macOS")
    IOS = (301, "iOS")
    IPADOS = (302, "iPadOS")
    SOLARIS = (400, "Solaris")
    AIX = (401, "AIX")
    HPUX = (402, "HP-UX")


class InstallState(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    INSTALLED = (1, "Installed")
    NOT_INSTALLED = (2, "Not Installed")
    INSTALLED_PENDING_REBOOT = (3, "Installed Pending Reboot")
    OTHER = (99, "Other")


class AgentType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    ENDPOINT_DETECTION_AND_RESPONSE = (1, "Endpoint Detection and Response")
    DATA_LOSS_PREVENTION = (2, "Data Loss Prevention")
    BACKUP_AND_RECOVERY = (3, "Backup & Recovery")
    PERFORMANCE_MONITORING_AND_OBSERVABILITY = (
        4,
        "Performance Monitoring & Observability",
    )
    VULNERABILITY_MANAGEMENT = (5, "Vulnerability Management")
    LOG_FORWARDING = (6, "Log Forwarding")
    MOBILE_DEVICE_MANAGEMENT = (7, "Mobile Device Management")
    CONFIGURATION_MANAGEMENT = (8, "Configuration Management")
    REMOTE_ACCESS = (9, "Remote Access")
    OTHER = (99, "Other")


class TicketType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    INTERNAL = (1, "Internal")
    EXTERNAL = (2, "External")
    OTHER = (99, "Other")


class HashAlgorithm(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    MD5 = (1, "MD5")
    SHA1 = (2, "SHA-1")
    SHA256 = (3, "SHA-256")
    SHA512 = (4, "SHA-512")
    CTPH = (5, "CTPH")
    TLSH = (6, "TLSH")
    QUICKXORHASH = (7, "QuickXorHash")
    OTHER = (99, "Other")


class UrlCategory(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    ADULT_MATURE_CONTENT = (1, "Adult/Mature Content")
    PORNOGRAPHY = (3, "Pornography")
    SEX_EDUCATION = (4, "Sex Education")
    INTIMATE_APPAREL_SWIMSUIT = (5, "Intimate Apparel/Swimsuit")
    NUDITY = (6, "Nudity")
    EXTREME = (7, "Extreme")
    SCAM_QUESTIONABLE_ILLEGAL = (9, "Scam/Questionable/Illegal")
    GAMBLING = (11, "Gambling")
    VIOLENCE_HATE_RACISM = (14, "Violence/Hate/Racism")
    WEAPONS = (15, "Weapons")
    ABORTION = (16, "Abortion")
    HACKING = (17, "Hacking")
    PHISHING = (18, "Phishing")
    ENTERTAINMENT = (20, "Entertainment")
    BUSINESS_ECONOMY = (21, "Business/Economy")
    ALTERNATIVE_SPIRITUALITY_BELIEF = (22, "Alternative Spirituality/Belief")
    ALCOHOL = (23, "Alcohol")
    TOBACCO = (24, "Tobacco")
    CONTROLLED_SUBSTANCES = (25, "Controlled Substances")
    CHILD_PORNOGRAPHY = (26, "Child Pornography")
    EDUCATION = (27, "Education")
    CHARITABLE_ORGANIZATIONS = (29, "Charitable Organizations")
    ART_CULTURE = (30, "Art/Culture")
    FINANCIAL_SERVICES = (31, "Financial Services")
    BROKERAGE_TRADING = (32, "Brokerage/Trading")
    GAMES = (33, "Games")
    GOVERNMENT_LEGAL = (34, "Government/Legal")
    MILITARY = (35, "Military")
    POLITICAL_SOCIAL_ADVOCACY = (36, "Political/Social Advocacy")
    HEALTH = (37, "Health")
    TECHNOLOGY_INTERNET = (38, "Technology/Internet")
    SEARCH_ENGINES_PORTALS = (40, "Search Engines/Portals")
    MALICIOUS_SOURCES_MALNETS = (43, "Malicious Sources/Malnets")
    MALICIOUS_OUTBOUND_DATA_BOTNETS = (44, "Malicious Outbound Data/Botnets")
    JOB_SEARCH_CAREERS = (45, "Job Search/Careers")
    NEWS_MEDIA = (46, "News/Media")
    PERSONALS_DATING = (47, "Personals/Dating")
    REFERENCE = (49, "Reference")
    MIXED_CONTENT_POTENTIALLY_ADULT = (50, "Mixed Content/Potentially Adult")
    CHAT_IM_SMS = (51, "Chat (IM)/SMS")
    EMAIL = (52, "Email")
    NEWSGROUPS_FORUMS = (53, "Newsgroups/Forums")
    RELIGION = (54, "Religion")
    SOCIAL_NETWORKING = (55, "Social Networking")
    FILE_STORAGE_SHARING = (56, "File Storage/Sharing")
    REMOTE_ACCESS_TOOLS = (57, "Remote Access Tools")
    SHOPPING = (58, "Shopping")
    AUCTIONS = (59, "Auctions")
    REAL_ESTATE = (60, "Real Estate")
    SOCIETY_DAILY_LIVING = (61, "Society/Daily Living")
    PERSONAL_SITES = (63, "Personal Sites")
    RESTAURANTS_DINING_FOOD = (64, "Restaurants/Dining/Food")
    SPORTS_RECREATION = (65, "Sports/Recreation")
    TRAVEL = (66, "Travel")
    VEHICLES = (67, "Vehicles")
    HUMOR_JOKES = (68, "Humor/Jokes")
    SOFTWARE_DOWNLOADS = (71, "Software Downloads")
    PEER_TO_PEER_P2P = (83, "Peer-to-Peer (P2P)")
    AUDIO_VIDEO_CLIPS = (84, "Audio/Video Clips")
    OFFICE_BUSINESS_APPLICATIONS = (85, "Office/Business Applications")
    PROXY_AVOIDANCE = (86, "Proxy Avoidance")
    FOR_KIDS = (87, "For Kids")
    WEB_ADS_ANALYTICS = (88, "Web Ads/Analytics")
    WEB_HOSTING = (89, "Web Hosting")
    UNCATEGORIZED = (90, "Uncategorized")
    SUSPICIOUS = (92, "Suspicious")
    SEXUAL_EXPRESSION = (93, "Sexual Expression")
    TRANSLATION = (95, "Translation")
    NON_VIEWABLE_INFRASTRUCTURE = (96, "Non-Viewable/Infrastructure")
    CONTENT_SERVERS = (97, "Content Servers")
    PLACEHOLDERS = (98, "Placeholders")
    OTHER = (99, "Other")
    SPAM = (101, "Spam")
    POTENTIALLY_UNWANTED_SOFTWARE = (102, "Potentially Unwanted Software")
    DYNAMIC_DNS_HOST = (103, "Dynamic DNS Host")
    E_CARD_INVITATIONS = (106, "E-Card/Invitations")
    INFORMATIONAL = (107, "Informational")
    COMPUTER_INFORMATION_SECURITY = (108, "Computer/Information Security")
    INTERNET_CONNECTED_DEVICES = (109, "Internet Connected Devices")
    INTERNET_TELEPHONY = (110, "Internet Telephony")
    ONLINE_MEETINGS = (111, "Online Meetings")
    MEDIA_SHARING = (112, "Media Sharing")
    RADIO_AUDIO_STREAMS = (113, "Radio/Audio Streams")
    TV_VIDEO_STREAMS = (114, "TV/Video Streams")
    PIRACY_COPYRIGHT_CONCERNS = (118, "Piracy/Copyright Concerns")
    MARIJUANA = (121, "Marijuana")


class EmailActivityDirection(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    INBOUND = (1, "Inbound")
    OUTBOUND = (2, "Outbound")
    INTERNAL = (3, "Internal")
    OTHER = (99, "Other")


class FileType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    REGULAR_FILE = (1, "Regular File")
    FOLDER = (2, "Folder")
    CHARACTER_DEVICE = (3, "Character Device")
    BLOCK_DEVICE = (4, "Block Device")
    LOCAL_SOCKET = (5, "Local Socket")
    NAMED_PIPE = (6, "Named Pipe")
    SYMBOLIC_LINK = (7, "Symbolic Link")
    OTHER = (99, "Other")


class DNSAnswerFlag(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    AUTHORITATIVE_ANSWER = (1, "Authoritative Answer")
    TRUNCATED_RESPONSE = (2, "Truncated Response")
    RECURSION_DESIRED = (3, "Recursion Desired")
    RECURSION_AVAILABLE = (4, "Recursion Available")
    AUTHENTIC_DATA = (5, "Authentic Data")
    CHECKING_DISABLED = (6, "Checking Disabled")
    OTHER = (99, "Other")


class OSINTIndicatorType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    IP_ADDRESS = (1, "IP Address")
    DOMAIN = (2, "Domain")
    HOSTNAME = (3, "Hostname")
    HASH = (4, "Hash")
    URL = (5, "URL")
    USER_AGENT = (6, "User Agent")
    DIGITAL_CERTIFICATE = (7, "Digital Certificate")
    EMAIL = (8, "Email")
    EMAIL_ADDRESS = (9, "Email Address")
    VULNERABILITY = (10, "Vulnerability")
    FILE = (11, "File")
    REGISTRY_KEY = (12, "Registry Key")
    REGISTRY_VALUE = (13, "Registry Value")
    COMMAND_LINE = (14, "Command Line")
    OTHER = (99, "Other")


class RiskLevel(OCSFEnum):
    INFO = (0, "Info")
    LOW = (1, "Low")
    MEDIUM = (2, "Medium")
    HIGH = (3, "High")
    CRITICAL = (4, "Critical")
    OTHER = (99, "Other")


class ReputationScore(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    # The reputation score is unknown.
    VERY_SAFE = (1, "Very Safe")
    # Long history of good behavior.
    SAFE = (2, "Safe")
    # Consistently good behavior.
    PROBABLY_SAFE = (3, "Probably Safe")
    # Reasonable history of good behavior.
    LEANS_SAFE = (4, "Leans Safe")
    # Starting to establish a history of normal behavior.
    MAY_NOT_BE_SAFE = (5, "May not be Safe")
    # No established history of normal behavior.
    EXERCISE_CAUTION = (6, "Exercise Caution")
    # Starting to establish a history of suspicious or risky behavior.
    SUSPICIOUS_RISKY = (7, "Suspicious/Risky")
    # A site with a history of suspicious or risky behavior.
    POSSIBLY_MALICIOUS = (8, "Possibly Malicious")
    # Strong possibility of maliciousness.
    PROBABLY_MALICIOUS = (9, "Probably Malicious")
    # Indicators of maliciousness.
    MALICIOUS = (10, "Malicious")
    # Proven evidence of maliciousness.
    OTHER = (99, "Other")
    # The reputation score is not mapped. See the rep_score attribute.


class Profile(StrEnum):
    CLOUD = "cloud"
    CONTAINER = "container"
    DATA_CLASSIFICATION = "data_classification"
    DATETIME = "datetime"
    HOST = "host"
    INCIDENT = "incident"
    LOAD_BALANCER = "load_balancer"
    NETWORK_PROXY = "network_proxy"
    OSINT = "osint"
    SECURITY_CONTROL = "security_control"
    TRACE = "trace"


class EndpointType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    SERVER = (1, "Server")
    DESKTOP = (2, "Desktop")
    LAPTOP = (3, "Laptop")
    TABLET = (4, "Tablet")
    MOBILE = (5, "Mobile")
    VIRTUAL = (6, "Virtual")
    IOT = (7, "IoT")
    BROWSER = (8, "Browser")
    FIREWALL = (9, "Firewall")
    SWITCH = (10, "Switch")
    HUB = (11, "Hub")
    ROUTER = (12, "Router")
    IDS = (13, "IDS")
    IPS = (14, "IPS")
    LOAD_BALANCER = (15, "Load Balancer")
    OTHER = (99, "Other")


class NetworkInterfaceType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    WIRED = (1, "Wired")
    WIRELESS = (2, "Wireless")
    MOBILE = (3, "Mobile")
    TUNNEL = (4, "Tunnel")
    OTHER = (99, "Other")


class ClassificationConfidentiality(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    NOT_CONFIDENTIAL = (1, "Not Confidential")
    CONFIDENTIAL = (2, "Confidential")
    SECRET = (3, "Secret")
    TOP_SECRET = (4, "Top Secret")
    PRIVATE = (5, "Private")
    RESTRICTED = (6, "Restricted")
    OTHER = (99, "Other")


class NetworkBoundary(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    LOCALHOST = (1, "Localhost")
    INTERNAL = (2, "Internal")
    EXTERNAL = (3, "External")
    SAME_VPC = (4, "Same VPC")
    INTERNET_VPC_GATEWAY = (5, "Internet/VPC Gateway")
    VIRTUAL_PRIVATE_GATEWAY = (6, "Virtual Private Gateway")
    INTRA_REGION_VPC = (7, "Intra-region VPC")
    INTER_REGION_VPC = (8, "Inter-region VPC")
    LOCAL_GATEWAY = (9, "Local Gateway")
    GATEWAY_VPC = (10, "Gateway VPC")
    INTERNET_GATEWAY = (11, "Internet Gateway")
    OTHER = (99, "Other")


class NetworkDirection(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    INBOUND = (1, "Inbound")
    OUTBOUND = (2, "Outbound")
    LATERAL = (3, "Lateral")
    OTHER = (99, "Other")


class ProtocolVersion(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    IPV4 = (4, "IPv4")
    IPV6 = (6, "IPv6")
    OTHER = (99, "Other")


class TLSExtensionType(OCSFEnum):
    SERVER_NAME = (0, "Server Name Indication")
    MAXIMUM_FRAGMENT_LENGTH = (1, "Maximum Fragment Length Negotiation")
    STATUS_REQUEST = (5, "Certificate Status Request")
    SUPPORTED_GROUPS = (10, "Supported Groups")
    SIGNATURE_ALGORITHMS = (13, "Signature Algorithms")
    USE_SRTP = (14, "Use SRTP")
    HEARTBEAT = (15, "Heartbeat")
    APPLICATION_LAYER_PROTOCOL_NEGOTIATION = (
        16,
        "Application Layer Protocol Negotiation",
    )
    SIGNED_CERTIFICATE_TIMESTAMP = (18, "Signed Certificate Timestamp")
    CLIENT_CERTIFICATE_TYPE = (19, "Client Certificate Type")
    SERVER_CERTIFICATE_TYPE = (20, "Server Certificate Type")
    PADDING = (21, "Padding")
    PRE_SHARED_KEY = (41, "Pre-shared Key")
    EARLY_DATA = (42, "Early Data")
    SUPPORTED_VERSIONS = (43, "Supported Versions")
    COOKIE = (44, "Cookie")
    PSK_KEY_EXCHANGE_MODES = (45, " PSK Key Exchange Modes")
    CERTIFICATE_AUTHORITIES = (47, "Certificate Authorities")
    OID_FILTERS = (48, "OID Filters")
    POST_HANDSHAKE_AUTH = (49, "Post-handshake Authentication")
    SIGNATURE_ALGORITHMS_CERT = (50, "Signature Algorithms Cert")
    KEY_SHARE = (51, "Key Share")
    OTHER = (99, "Other")


class SoftwarePackageType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    APPLICATION = (1, "Application")
    OPERATING_SYSTEM = (2, "Operating System")
    OTHER = (99, "Other")


class SBOMType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    SPDX = (1, "SPDX")
    CYCLONEDX = (2, "CycloneDX")
    SWID = (3, "SWID")
    OTHER = (99, "Other")


class SoftwareComponentRelationship(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    DEPENDS_ON = (1, "Depends On")
    OTHER = (99, "Other")


class SoftwareComponentType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    FRAMEWORK = (1, "Framework")
    LIBRARY = (2, "Library")
    OPERATING_SYSTEM = (3, "Operating System")
    OTHER = (99, "Other")


class QueryLanguage(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    CYPHER = (1, "Cypher")
    GRAPHQL = (2, "GraphQL")
    GREMLIN = (3, "Gremlin")
    GQL = (4, "GQL")
    G_CORE = (5, "G-CORE")
    PGQL = (6, "PGQL")
    SPARQL = (7, "SPARQL")
    OTHER = (99, "Other")


class AuthenticationFactorType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    SMS = (1, "SMS")
    SECURITY_QUESTION = (2, "Security Question")
    PHONE_CALL = (3, "Phone Call")
    BIOMETRIC = (4, "Biometric")
    PUSH_NOTIFICATION = (5, "Push Notification")
    HARDWARE_TOKEN = (6, "Hardware Token")
    OTP = (7, "OTP")
    EMAIL = (8, "Email")
    U2F = (9, "U2F")
    WEBAUTHN = (10, "WebAuthn")
    PASSWORD = (11, "Password")
    OTHER = (99, "Other")


class AuthenticationTokenType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    TICKET_GRANTING_TICKET = (1, "Ticket Granting Ticket")
    SERVICE_TICKET = (2, "Service Ticket")
    IDENTITY_TOKEN = (3, "Identity Token")
    REFRESH_TOKEN = (4, "Refresh Token")
    SAML_ASSERTION = (5, "SAML Assertion")
    OTHER = (99, "Other")


class EncryptionAlgorithm(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    DES = (1, "DES")
    TRIPLE_DES = (2, "TripleDES")
    AES = (3, "AES")
    RSA = (4, "RSA")
    ECC = (5, "ECC")
    SM2 = (6, "SM2")
    OTHER = (99, "Other")


class ComplianceStatus(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    PASS = (1, "Pass")
    WARNING = (2, "Warning")
    FAIL = (3, "Fail")
    OTHER = (99, "Other")


class DataLifecycleState(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    DATA_AT_REST = (1, "Data at-Rest")
    DATA_IN_TRANSIT = (2, "Data in-Transit")
    DATA_IN_USE = (3, "Data in-Use")
    OTHER = (99, "Other")


class DetectionSystem(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    DLP = (1, "DLP")
    DLD = (2, "DLD")
    OTHER = (99, "Other")


class DatabaseType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    RELATIONAL = (1, "Relational")
    NETWORK = (2, "Network")
    OBJECT_ORIENTED = (3, "Object Oriented")
    CENTRALIZED = (4, "Centralized")
    OPERATIONAL = (5, "Operational")
    NOSQL = (6, "NoSQL")
    OTHER = (99, "Other")


class DatabucketType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    S3 = (1, "S3")
    AZURE_BLOB = (2, "Azure Blob")
    GCP_BUCKET = (3, "GCP Bucket")
    OTHER = (99, "Other")


class DigitalSignatureAlgorithm(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    DSA = (1, "DSA")
    RSA = (2, "RSA")
    ECDSA = (3, "ECDSA")
    AUTHENTICODE = (4, "Authenticode")
    OTHER = (99, "Other")


class DigitalSignatureState(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    VALID = (1, "Valid")
    EXPIRED = (2, "Expired")
    REVOKED = (3, "Revoked")
    SUSPENDED = (4, "Suspended")
    PENDING = (5, "Pending")
    OTHER = (99, "Other")


class JobRunState(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    READY = (1, "Ready")
    QUEUED = (2, "Queued")
    RUNNING = (3, "Running")
    STOPPED = (4, "Stopped")
    OTHER = (99, "Other")


class ScanType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    MANUAL = (1, "Manual")
    SCHEDULED = (2, "Scheduled")
    UPDATED_CONTENT = (3, "Updated Content")
    QUARANTINED_ITEMS = (4, "Quarantined Items")
    ATTACHED_MEDIA = (5, "Attached Media")
    USER_LOGON = (6, "User Logon")
    ELAM = (7, "ELAM")
    OTHER = (99, "Other")


class IntegrityLevel(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    UNTRUSTED = (1, "Untrusted")
    LOW = (2, "Low")
    MEDIUM = (3, "Medium")
    HIGH = (4, "High")
    SYSTEM = (5, "System")
    PROTECTED = (6, "Protected")
    OTHER = (99, "Other")


class AuthProtocol(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    NTLM = (1, "NTLM")
    KERBEROS = (2, "Kerberos")
    DIGEST = (3, "Digest")
    OPENID = (4, "OpenID")
    SAML = (5, "SAML")
    OAUTH_2_0 = (6, "OAUTH 2.0")
    PAP = (7, "PAP")
    CHAP = (8, "CHAP")
    EAP = (9, "EAP")
    RADIUS = (10, "RADIUS")
    BASIC_AUTHENTICATION = (11, "Basic Authentication")
    LDAP = (12, "LDAP")
    OTHER = (99, "Other")


class SCIMState(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    PENDING = (1, "Pending")
    ACTIVE = (2, "Active")
    FAILED = (3, "Failed")
    DELETED = (4, "Deleted")
    OTHER = (99, "Other")


class FixCoverage(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    COMPLETE = (1, "Complete")
    PARTIAL = (2, "Partial")
    NONE = (3, "None")
    OTHER = (99, "Other")


class Opcode(OCSFEnum):
    QUERY = (0, "Query")
    INVERSE_QUERY = (1, "Inverse Query")
    STATUS = (2, "Status")
    RESERVED = (3, "Reserved")
    NOTIFY = (4, "Notify")
    UPDATE = (5, "Update")
    DSO_MESSAGE = (6, "DSO Message")
    OTHER = (99, "Other")


class JA4FingerprintType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    JA4 = (1, "JA4")
    JA4SERVER = (2, "JA4Server")
    JA4HTTP = (3, "JA4HTTP")
    JA4LATENCY = (4, "JA4Latency")
    JA4X509 = (5, "JA4X509")
    JA4SSH = (6, "JA4SSH")
    JA4TCP = (7, "JA4TCP")
    JA4TCPSERVER = (8, "JA4TCPServer")
    JA4TCPSCAN = (9, "JA4TCPScan")
    OTHER = (99, "Other")


class RegistryValueType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    REG_BINARY = (1, "REG_BINARY")
    REG_DWORD = (2, "REG_DWORD")
    REG_DWORD_BIG_ENDIAN = (3, "REG_DWORD_BIG_ENDIAN")
    REG_EXPAND_SZ = (4, "REG_EXPAND_SZ")
    REG_LINK = (5, "REG_LINK")
    REG_MULTI_SZ = (6, "REG_MULTI_SZ")
    REG_NONE = (7, "REG_NONE")
    REG_QWORD = (8, "REG_QWORD")
    REG_QWORD_LITTLE_ENDIAN = (9, "REG_QWORD_LITTLE_ENDIAN")
    REG_SZ = (10, "REG_SZ")
    OTHER = (99, "Other")


class ScriptType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    WINDOWS_COMMAND_PROMPT = (1, "Windows Command Prompt")
    POWERSHELL = (2, "PowerShell")
    PYTHON = (3, "Python")
    JAVASCRIPT = (4, "JavaScript")
    VBSCRIPT = (5, "VBScript")
    UNIX_SHELL = (6, "Unix Shell")
    VBA = (7, "VBA")
    OTHER = (99, "Other")


class ServiceErrorControl(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    IGNORE = (1, "Ignore")
    NORMAL = (2, "Normal")
    SEVERE = (3, "Severe")
    CRITICAL = (4, "Critical")
    OTHER = (99, "Other")


class ServiceStartType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    BOOT = (1, "Boot")
    SYSTEM = (2, "System")
    AUTO = (3, "Auto")
    DEMAND = (4, "Demand")
    DISABLED = (5, "Disabled")
    OTHER = (99, "Other")


class ServiceType(OCSFEnum):
    UNKNOWN = (0, "Unknown")
    KERNEL_DRIVER = (1, "Kernel Driver")
    FILE_SYSTEM_DRIVER = (2, "File System Driver")
    OWN_PROCESS = (3, "Own Process")
    SHARE_PROCESS = (4, "Share Process")
    OTHER = (99, "Other")

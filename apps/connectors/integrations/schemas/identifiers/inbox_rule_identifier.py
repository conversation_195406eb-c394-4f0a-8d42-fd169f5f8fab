from typing import Literal

from apps.connectors.integrations.schemas.action_args import IntegrationIdentifierArgs

from .identifier import Identifier, IdentifierType

"""
Represents attributes required to identify an inbox rule in a source technology.
"""


class InboxRuleIdentifier(Identifier):
    identifier_type: Literal[IdentifierType.INBOX_RULE] = IdentifierType.INBOX_RULE


class InboxRuleIdentifierArgs(IntegrationIdentifierArgs):
    inbox_rule: InboxRuleIdentifier

from typing import Literal

from apps.connectors.integrations.schemas.action_args import IntegrationIdentifierArgs

from .identifier import Identifier, IdentifierType

"""
Represents attributes required to identify endpoint threat in a source technology.
"""


class EndpointThreatIdentifier(Identifier):
    identifier_type: Literal[
        IdentifierType.ENDPOINT_THREAT
    ] = IdentifierType.ENDPOINT_THREAT


class EndpointThreatIdentifierArgs(IntegrationIdentifierArgs):
    endpoint_threat: EndpointThreatIdentifier

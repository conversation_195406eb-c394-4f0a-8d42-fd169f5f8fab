from typing import Literal

from apps.connectors.integrations.schemas.action_args import IntegrationIdentifierArgs

from .identifier import Identifier, IdentifierType

"""
Represents attributes required to identify an email threat in a source technology such as
Abnormal Security, Proofpoint, Mimecast, etc.
"""


class EmailThreatIdentifier(Identifier):
    identifier_type: Literal[IdentifierType.EMAIL_THREAT] = IdentifierType.EMAIL_THREAT


class EmailThreatIdentifierArgs(IntegrationIdentifierArgs):
    email_threat_id: EmailThreatIdentifier

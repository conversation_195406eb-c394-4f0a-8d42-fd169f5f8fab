from enum import StrEnum
from typing import Optional

from pydantic import BaseModel, Field


class TechnologyIdentifierType(StrEnum):
    USER = "user_identifier"
    EMAIL_THREAT = "email_threat_identifier"
    HOST = "host_identifier"
    MAIL_MESSAGE = "mail_message_identifier"
    INBOX_RULE = "inbox_rule_identifier"
    ENDPOINT_THREAT = "endpoint_threat_identifier"
    MAILBOX_RULE = "mailbox_rule_identifier"


class PrimitiveIdentifierType(StrEnum):
    DOMAIN_NAME = "domain_name_identifier"
    FILE_HASH = "file_hash_identifier"
    IP_ADDRESS = "ip_address_identifier"
    UPN = "upn_identifier"
    URL = "url_identifier"


# Dynamically create IdentifierType by combining members from both
IdentifierType = StrEnum(
    "IdentifierType",
    {**TechnologyIdentifierType.__members__, **PrimitiveIdentifierType.__members__},
)


class Identifier(BaseModel):
    """
    Represents an entity identifier that can serve as either a primary source ID or
    a secondary ID that is compatible with a source system.

    The identifier always supports a technology-specific ID, and the `value_type` field
    indicates the type of the identifier. Subclasses can extend the supported value types
    by defining additional valid values (via a StrEnum) for `value_type`.
    """

    identifier_type: IdentifierType

    value_type: Optional[str] = Field(
        description="Clarifies the format of the `value`. If the value is a primitive type, "
        "this will be None. Otherwise, it will specify the related technology_id "
        "used to interpret the value.",
    )
    value: str = Field(
        description="A non-empty string that holds the actual identifier. This is the unique "
        "value that represents the entity within the designated system or integration.",
        min_length=1,
    )

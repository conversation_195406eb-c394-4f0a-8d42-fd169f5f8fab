from typing import Literal

from apps.connectors.integrations.schemas.action_args import IntegrationIdentifierArgs

from .identifier import Identifier, IdentifierType

"""
Represents attributes required to identify a host in a source technology.
"""


class HostIdentifier(Identifier):
    identifier_type: Literal[IdentifierType.HOST] = IdentifierType.HOST


class HostIdentifierArgs(IntegrationIdentifierArgs):
    host: HostIdentifier

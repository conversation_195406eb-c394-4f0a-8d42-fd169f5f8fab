from typing import Literal

from apps.connectors.integrations.schemas.action_args import IntegrationIdentifierArgs

from .identifier import Identifier, IdentifierType

"""
Represents attributes required to identify a user in a source technology.
"""


class UserIdentifier(Identifier):
    identifier_type: Literal[IdentifierType.USER] = IdentifierType.USER


class UserIdentifierArgs(IntegrationIdentifierArgs):
    user_id: UserIdentifier

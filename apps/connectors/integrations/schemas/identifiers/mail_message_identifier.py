from typing import Literal

from apps.connectors.integrations.schemas.action_args import IntegrationIdentifierArgs

from .identifier import Identifier, IdentifierType

"""
Represents attributes required to identify a mail message in a source technology.
"""


class MailMessageIdentifier(Identifier):
    identifier_type: Literal[IdentifierType.MAIL_MESSAGE] = IdentifierType.MAIL_MESSAGE


class MailMessageIdentifierArgs(IntegrationIdentifierArgs):
    mail_message: MailMessageIdentifier

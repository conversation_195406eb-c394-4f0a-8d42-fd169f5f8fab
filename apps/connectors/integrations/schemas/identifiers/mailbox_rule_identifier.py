from enum import StrEnum
from typing import Literal

from .identifier import Identifier, IdentifierType

"""
Represents attributes required to identify a mailbox rule in a source technology.
"""


class MailboxRuleIdType(StrEnum):
    RULE_NAME = "rule_name"  # The name of the rule


class MailboxRuleIdentifier(Identifier):
    identifier_type: Literal[IdentifierType.MAILBOX_RULE] = IdentifierType.MAILBOX_RULE

from abc import ABC, abstractmethod
from enum import StrEnum


class ValidationStatus(StrEnum):
    PASSED = "passed"
    FAILED = "failed"
    MISCONFIGURED = "misconfigured"
    UNKNOWN = "unknown"


class RequirementStatus(StrEnum):
    REQUIRED = "required"
    OPTIONAL = "optional"


class HealthCheck(ABC):
    name: str
    description: str
    value: str | None
    required: RequirementStatus

    @abstractmethod
    def get_integration(self):
        ...

    @abstractmethod
    def get_result(self) -> ValidationStatus:
        ...

from apps.connectors.health_checks.base import (
    Health<PERSON>heck,
    RequirementStatus,
    ValidationStatus,
)
from apps.connectors.integrations.actions.action import TechnologyIntegrationActionType


class ConnectorHealthCheck(HealthCheck):
    """
    Base class for health checks that are specific to a connector.
    """

    def __init__(self, connector):
        self.connector = connector

    def get_integration(self):
        return self.connector.get_integration()


class ActionDependencyHealthCheck(ConnectorHealthCheck):
    """
    Health checks that checks if specific action is enabled.
    """

    name = "Required action"
    value = None
    required = RequirementStatus.REQUIRED

    def __init__(
        self,
        connector,
        required_technology_action: TechnologyIntegrationActionType,
    ):
        super().__init__(connector)
        self.required_technology_action = required_technology_action
        self.description = f"{required_technology_action.action_type} for {required_technology_action.technology_id} must be enabled and healthy"

    def get_result(self) -> ValidationStatus:
        from apps.connectors.models import Connector
        from apps.connectors.services import connector_service

        connectors = Connector.objects.with_enabled_action(
            organization_id=self.connector.organization_id,
            action_type=self.required_technology_action.action_type,
        )
        for connector in connectors:
            if not connector_service.is_action_healthy(
                connector, self.required_technology_action.action_type
            ):
                return ValidationStatus.FAILED
        return ValidationStatus.PASSED if connectors else ValidationStatus.FAILED

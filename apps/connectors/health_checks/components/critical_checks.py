from apps.connectors.health_checks.components.component import (
    AccessType,
    ComponentType,
    HealthCheckComponentTemplate,
)


class CriticalChecks(HealthCheckComponentTemplate):
    id = "critical-config-valid"
    name = "Critical checks"
    link = None
    meta = {
        "fields": [
            {"field": "status", "label": "Status", "order": 0},
            {"field": "name", "name": "label", "order": 1},
            {"field": "required", "label": "Required", "order": 2},
            {"field": "description", "label": "Description", "order": 3},
        ],
        "access": AccessType.INTERNAL,
        "info_url": None,
    }
    component_type = ComponentType.EXECUTION

    def get_health_checks(self):
        integration = self.connector.get_integration()
        return integration.get_critical_health_checks()

from typing import Iterable

from apps.connectors.health_checks.components.component import (
    AccessType,
    ComponentType,
    HealthCheckComponentTemplate,
)
from apps.connectors.health_checks.health_check import (
    ActionDependencyHealthCheck,
    ConnectorHealthCheck,
)
from apps.connectors.integrations.actions.action import (
    IntegrationActionType,
    TechnologyIntegrationActionType,
)
from apps.connectors.integrations.vendors.vendor import Vendors


class MsEventSyncDependencyChecks(HealthCheckComponentTemplate):
    id: str = "ms_event_sync_dependency_checks"
    name: str = "Microsoft Event Sync Dependency Checks"
    action_type = "event_sync_from_artifact"

    link = None
    meta = {
        "fields": [
            {"field": "status", "label": "Status", "order": 0},
            {"field": "name", "label": "Name", "order": 1},
            {"field": "value", "label": "Requires Action", "order": 2},
            {"field": "required", "label": "Required", "order": 3},
            {"field": "description", "label": "Description", "order": 4},
        ],
        "access": AccessType.INTERNAL,
        "info_url": None,
    }
    component_type = ComponentType.DEPENDENCY

    def get_health_checks(self) -> Iterable[ConnectorHealthCheck]:
        if self.connector.template.vendor.id != Vendors.MICROSOFT.id:
            return []
        if self.action_type not in self.connector.enabled_actions:
            return []

        ms_xdr_event_sync_action = TechnologyIntegrationActionType(
            technology_id="ms_xdr",
            action_type=IntegrationActionType.EVENT_SYNC,
        )
        return [
            ActionDependencyHealthCheck(
                connector=self.connector,
                required_technology_action=ms_xdr_event_sync_action,
            )
        ]

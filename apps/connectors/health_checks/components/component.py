import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import StrEnum
from typing import Iterable

from dataclasses_json import config, dataclass_json
from django.utils import timezone

from apps.connectors.health_checks.base import HealthCheck

logger = logging.getLogger(__name__)


class ValidationStatus(StrEnum):
    PASSED = "passed"
    FAILED = "failed"
    MISCONFIGURED = "misconfigured"
    UNKNOWN = "unknown"


class AccessType(StrEnum):
    PUBLIC = "public"
    INTERNAL = "internal"


class RequirementStatus(StrEnum):
    REQUIRED = "required"
    OPTIONAL = "optional"


class ComponentType(StrEnum):
    PERMISSION = "permission"
    ROLE_ASSIGNMENT = "role_assignment"
    EXECUTION = "execution"  # e.g. "can execute a test query"
    DATA = "data"  # e.g. "has at least 1 user"
    DEPENDENCY = "dependency"  # e.g. "requires another integration to be healthy"


@dataclass_json
@dataclass
class HealthCheckRequirement:
    name: str
    description: str
    value: str | None
    required: RequirementStatus
    status: ValidationStatus

    def is_healthy(self):
        return self.status == ValidationStatus.PASSED

    @classmethod
    def from_integration(cls, check: HealthCheck, results):
        activity_logger = check.get_integration().logger
        try:
            # keep track of results to avoid running the same check multiple times
            if (result := results.get(check)) is None:
                result = check.get_result()
                if result == ValidationStatus.PASSED:
                    activity_logger.info(f"Passed health check '{check.name}'")
                else:
                    activity_logger.error(
                        f"Failed health check '{check.name}' with status '{result}'"
                    )
        except Exception:
            logger.exception(f"Error while running health check {check.name}")
            activity_logger.error(f"Error while running health check '{check.name}'")
            result = ValidationStatus.FAILED

        results[check] = result

        return cls(
            name=check.name,
            description=check.description,
            value=check.value,
            required=check.required,
            status=result,
        )


class HealthCheckComponentTemplate(ABC):
    _templates: list[type["HealthCheckComponentTemplate"]] = []

    id: str
    name: str
    link: str | None
    meta: dict
    component_type: ComponentType
    # Set if the health check applies to a specific action type
    action_type = None

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        if hasattr(cls, "id"):
            cls._templates.append(cls)

    def __init__(self, connector=None):
        self.connector = connector

    @classmethod
    def get_templates(
        cls, connector=None, component_type=None
    ) -> Iterable["HealthCheckComponentTemplate"]:
        templates = [template(connector=connector) for template in cls._templates]
        if component_type:
            templates = [t for t in templates if t.component_type == component_type]
        return templates

    @abstractmethod
    def get_health_checks(self) -> Iterable[HealthCheck]:
        ...

    def get_requirements(self, results) -> list[HealthCheckRequirement]:
        return [
            HealthCheckRequirement.from_integration(check, results)
            for check in self.get_health_checks()
        ]


def wrap_now():
    # Wrapping now allows us to mock it in tests
    return timezone.now()


@dataclass_json
@dataclass
class HealthCheckComponent:
    id: str
    name: str
    link: str
    meta: dict
    type: ComponentType
    updated: datetime = field(
        default_factory=wrap_now,
        metadata=config(
            encoder=datetime.isoformat,
            decoder=datetime.fromisoformat,
        ),
    )
    requirements: list[HealthCheckRequirement] = field(default_factory=list)
    action_type: str | None = None

    @classmethod
    def get_components(cls, connector, component_type=None):
        templates = HealthCheckComponentTemplate.get_templates(
            connector=connector, component_type=component_type
        )
        results = {}
        return [
            component
            for template in templates
            if (component := cls.from_template(template, results)).requirements
        ]

    @classmethod
    def from_template(cls, template: HealthCheckComponentTemplate, results):
        return cls(
            id=template.id,
            name=template.name,
            link=template.link,
            meta=template.meta,
            type=template.component_type,
            requirements=template.get_requirements(results),
            action_type=template.action_type,
        )

    def is_healthy(self):
        return all(r.is_healthy() for r in self.requirements)

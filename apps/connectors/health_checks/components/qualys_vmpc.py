from apps.connectors.health_checks.components.component import (
    AccessType,
    ComponentType,
    HealthCheckComponentTemplate,
)
from apps.connectors.integrations.vendors.qualys.qualys_vmpc import QualysVmpcTemplate


class QualysVmpcV20EulaChecks(HealthCheckComponentTemplate):
    id = "eula-accepted"
    name = "EULA checks"
    link = None
    meta = {
        "fields": [
            {"field": "status", "label": "Status", "order": 0},
            {"field": "name", "label": "Name", "order": 1},
            {"field": "required", "label": "Required", "order": 2},
            {"field": "description", "label": "Description", "order": 3},
        ],
        "access": AccessType.INTERNAL,
        "info_url": None,
    }
    component_type = ComponentType.EXECUTION

    def get_health_checks(self):
        if self.connector.template.id != QualysVmpcTemplate.id:
            return []

        integration = self.connector.get_integration()
        return integration.get_eula_health_checks()

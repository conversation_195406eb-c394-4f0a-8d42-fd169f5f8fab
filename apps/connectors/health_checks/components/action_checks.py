import types
from collections.abc import Iterable

from apps.connectors.health_checks.components.component import (
    AccessType,
    ComponentType,
    HealthCheckComponentTemplate,
)
from apps.connectors.integrations import IntegrationAction, IntegrationActionType
from apps.connectors.integrations.health_check import IntegrationHealthCheck


class ActionChecksMixin:
    id: str
    name: str

    link = None
    meta = {
        "fields": [
            {"field": "status", "label": "Status", "order": 0},
            {"field": "name", "label": "Name", "order": 1},
            {"field": "value", "label": "Permission", "order": 2},
            {"field": "required", "label": "Required", "order": 3},
            {"field": "description", "label": "Description", "order": 4},
        ],
        "access": AccessType.INTERNAL,
        "info_url": None,
    }
    component_type = ComponentType.PERMISSION

    def get_health_checks(self) -> Iterable[IntegrationHealthCheck]:
        if self.id not in self.connector.enabled_actions:
            return []

        integration = self.connector.get_integration()
        return integration.get_action_health_checks(self.id)


# Ensure that these component checks are created using the ordering defined in IntegrationActionType before
# adding any namespaced actions, this preserves compatibility with existing tests which expect this ordering.
all_actions = list(IntegrationActionType._value2member_map_.keys())
for action in sorted(
    IntegrationAction.get_all_action_types(), key=lambda x: x.action_type
):
    if action.action_type.value not in all_actions:
        all_actions.append(action.action_type.value)

# For each IntegrationActionType, create a type in this module that inherits from ActionChecksMixin
# and HealthCheckComponentTemplate. Set the id to the IntegrationActionType value.
for action_type in all_actions:
    class_name = (
        "".join(word.capitalize() for word in action_type.split("_")) + "ActionChecks"
    )
    class_attrs = {
        "id": action_type,
        "action_type": action_type,
        "name": IntegrationAction.get_name(action_type),
    }
    new_class = types.new_class(
        class_name,
        (ActionChecksMixin, HealthCheckComponentTemplate),
        exec_body=lambda ns: ns.update(class_attrs),
    )

    globals()[class_name] = new_class
